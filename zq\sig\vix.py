from nb_common import *
from ut_redis import FrRedis
import math
from scipy.optimize import minimize
from scipy.stats import norm
from scipy.optimize import newton
from sys import platform

underlying_dict = {
    '510050': '50',
    '510300': '300',
    '510500': '500',
    '159919': '919',
    '159922': '922',
    '588000': 'k50',
    '588080': 'k58',
    '159915': '915',
    '159901': '901'
}

SEVER53 = r'\\***********\share\MD' if sys == 'win32' else '/A/MD/'

rd170 = FrRedis("************")
rd115 = FrRedis("************")
rd181 = FrRedis("************:6379:9")


def get_preopen_op_info():
    all_op_list = rd170.keys("OPLST:*")
    op_idx = []
    exp_dt = []
    contract = []
    strike = []
    option_type = []
    ma = []
    for con in all_op_list:
        op_info = rd170.hgetall(con)
        op_idx.append(op_info['InstrumentID'])
        exp_dt.append(datetime.datetime(int(op_info['ExpireDate'][:4]), int(op_info['ExpireDate'][4:6]),
                                        int(op_info['ExpireDate'][6:])))
        contract.append(op_info['UnderlyingInstrID'])
        strike.append(float(op_info['StrikePrice']))
        option_type.append('C' if int(op_info['OptionsType']) == 1 else 'P')
        ma.append('M' if 'M' in op_info['InstrumentCode'] else 'A')
    op_info = {}
    op_info['id'] = op_idx
    op_info['exp_dt'] = exp_dt
    op_info['exp_dt_seq'] = exp_dt
    op_info['contract'] = contract
    op_info['strike'] = strike
    op_info['option_type'] = option_type
    op_info['ma'] = ma
    op_info = pd.DataFrame(op_info)

    curr_mon = np.sort(op_info.exp_dt.unique())[0]
    next_mon = np.sort(op_info.exp_dt.unique())[1]
    op_info = op_info[
        (op_info.contract == underlying) & ((op_info.exp_dt == curr_mon) | (op_info.exp_dt == next_mon)) & (
                    op_info.ma == 'M')]
    op_info = op_info.set_index('id')

    op_info.loc[op_info.exp_dt == curr_mon, 'exp_dt_seq'] = 0
    op_info.loc[op_info.exp_dt == next_mon, 'exp_dt_seq'] = 1

    op_pair = {}
    for con in op_info.index:
        the_strike = op_info.loc[con, 'strike']
        the_mon = op_info.loc[con, 'exp_dt_seq']
        c, p = op_info[(op_info.strike == the_strike) & (op_info.exp_dt_seq == the_mon)].sort_values(
            by=['option_type']).index
        if c in op_pair:
            op_pair[p] = c
        else:
            op_pair[c] = p

    exp_dt1, exp_dt2 = np.sort(op_info.exp_dt.unique().astype(str))
    yr, mon, d = [int(comp) for comp in exp_dt1[:10].split('-')]
    exp_dt1 = datetime.datetime(yr, mon, d, 16, 0, 0)
    yr, mon, d = [int(comp) for comp in exp_dt2[:10].split('-')]
    exp_dt2 = datetime.datetime(yr, mon, d, 16, 0, 0)

    return op_info, op_pair, (exp_dt1, exp_dt2)


# def get_op_price(con, type='midprice', multiplier=1e-4):
#     if type == 'bid':
#         op_price = float(rd170.hget(f"MD:01{con}", "BP1")) * multiplier
#     elif type == 'ask':
#         op_price = float(rd170.hget(f"MD:01{con}", "SP1")) * multiplier
#     elif type == 'midprice':
#         op_price = (float(rd170.hget(f"MD:01{con}", "BP1")) * multiplier + float(
#             rd170.hget(f"MD:01{con}", "SP1")) * multiplier) / 2
#     if op_price < 1e-8:
#         op_price = float(rd170.hget(f"MD:01{con}", "Latest")) * multiplier
#         print('################## ABNORNAL: ' + str(con) + ' Fill With Latest ##################')
#     if op_price < 1e-8:
#         op_price = float(rd170.hget(f"MD:01{con}", "PreSettle")) * multiplier
#         print('################## ABNORNAL: ' + str(con) + ' Fill With PreSettle ##################')
#     return op_price



def get_op_price(con, type='midprice', multiplier=1e-4):
    if type == 'bid':
        op_price = float(rd170.hget(f"MD:01{con}", "BP1"))*multiplier
    elif type == 'ask':
        op_price = float(rd170.hget(f"MD:01{con}", "SP1"))*multiplier
    elif type == 'midprice':
        op_price_bid = float(rd170.hget(f"MD:01{con}", "BP1"))
        op_price_ask = float(rd170.hget(f"MD:01{con}", "SP1"))
        spread = op_price_ask - op_price_bid
        if spread < 30:
            op_price = (op_price_bid*multiplier+op_price_ask*multiplier)/2
        else:
            op_price = float(rd170.hget(f"MD:01{con}", "Latest"))*multiplier
    if op_price < 1e-8:
        op_price = float(rd170.hget(f"MD:01{con}", "Latest"))*multiplier
        print('################## ABNORNAL: '+str(con)+' Fill With Latest ##################')
    if op_price < 1e-8:
        op_price = float(rd170.hget(f"MD:01{con}", "PreSettle"))*multiplier
        print('################## ABNORNAL: '+str(con)+' Fill With PreSettle ##################')
    return op_price



def replenish_op_contracts(df):
    # print(df)
    additional = []
    for idx in range(df.shape[0] - 1):
        k1, k2 = df.iloc[idx, 3], df.iloc[idx + 1, 3]
        t = df.iloc[idx, 0]
        t_seq = df.iloc[idx, 1]
        con = df.iloc[idx, 2]
        con_type1, con_type2 = df.iloc[idx, 4], df.iloc[idx + 1, 4]
        ma = df.iloc[idx, 5]
        p1, p2 = df.iloc[idx, 6], df.iloc[idx + 1, 6]
        if con_type1 == con_type2:
            for k in np.linspace(k1, k2, round((k2 - k1) * 1e2) + 1):
                new_price = (p2 * (k - k1) + p1 * (k2 - k)) / (k2 - k1)
                new_line = ['interpolated', t, t_seq, con, k, con_type1, ma, new_price]
                if k not in [k1, k2]:
                    additional.append(new_line)
    additional = pd.DataFrame(np.array(additional), columns=df.reset_index().columns)

    added_df = pd.concat([additional, df.reset_index()]) \
        .sort_values(by=['option_type', 'strike'], ascending=[False, True])
    added_df.strike = added_df.strike.astype(float)
    return added_df.reset_index(drop=True)


def get_vix(op_info, op_pair, exp_dts):
    curr_time = datetime.datetime.now()

    T1 = (exp_dts[0] - curr_time).total_seconds() / (365 * 24 * 60 * 60)
    T2 = (exp_dts[1] - curr_time).total_seconds() / (365 * 24 * 60 * 60)

    R = 0.02

    curr_op_info = (op_info[op_info.exp_dt_seq == 0]).sort_values(by=['option_type', 'strike'], ascending=[False, True])
    next_op_info = (op_info[op_info.exp_dt_seq == 1]).sort_values(by=['option_type', 'strike'], ascending=[False, True])

    curr_op_info['price'] = [get_op_price(con, type='midprice', multiplier=1e-4) for con in curr_op_info.index]
    next_op_info['price'] = [get_op_price(con, type='midprice', multiplier=1e-4) for con in next_op_info.index]

    curr_op_info = replenish_op_contracts(curr_op_info)
    next_op_info = replenish_op_contracts(next_op_info)

    op_info = pd.concat([curr_op_info, next_op_info]).reset_index(drop=True)

    etf_price = int(rd115.get(f"KZ:S{underlying}:LATEST")) / 1e4
    op_info['distance'] = op_info.strike - etf_price
    curr_otm_op_info = pd.concat(
        [op_info[(op_info.exp_dt_seq == 0) & (op_info.option_type == 'C') & (op_info.distance >= -1e-6)], \
         op_info[(op_info.exp_dt_seq == 0) & (op_info.option_type == 'P') & (op_info.distance <= 1e-6)]]) \
        .sort_values(by=['strike', 'option_type'], ascending=[True, False]).reset_index(drop=True)
    next_otm_op_info = pd.concat(
        [op_info[(op_info.exp_dt_seq == 1) & (op_info.option_type == 'C') & (op_info.distance >= -1e-6)], \
         op_info[(op_info.exp_dt_seq == 1) & (op_info.option_type == 'P') & (op_info.distance <= 1e-6)]]) \
        .sort_values(by=['strike', 'option_type'], ascending=[True, False]).reset_index(drop=True)

    curr_otm_op_info['counter_contract_price'] = [
        get_op_price(op_pair[con], type='midprice', multiplier=1e-4) if con != 'interpolated' else None for con in
        curr_otm_op_info.id]
    next_otm_op_info['counter_contract_price'] = [
        get_op_price(op_pair[con], type='midprice', multiplier=1e-4) if con != 'interpolated' else None for con in
        next_otm_op_info.id]

    curr_otm_op_info['F'] = [
        curr_otm_op_info['price'].iloc[idx] - curr_otm_op_info['counter_contract_price'].iloc[idx] +
        curr_otm_op_info['strike'].iloc[idx] \
            if curr_otm_op_info['option_type'].iloc[idx] == 'C' \
            else curr_otm_op_info['counter_contract_price'].iloc[idx] - curr_otm_op_info['price'].iloc[idx] +
                 curr_otm_op_info['strike'].iloc[idx] \
        for idx in range(curr_otm_op_info.shape[0])]

    next_otm_op_info['F'] = [
        next_otm_op_info['price'].iloc[idx] - next_otm_op_info['counter_contract_price'].iloc[idx] +
        next_otm_op_info['strike'].iloc[idx] \
            if next_otm_op_info['option_type'].iloc[idx] == 'C' \
            else next_otm_op_info['counter_contract_price'].iloc[idx] - next_otm_op_info['price'].iloc[idx] +
                 next_otm_op_info['strike'].iloc[idx] \
        for idx in range(next_otm_op_info.shape[0])]
    F1 = curr_otm_op_info['F'].dropna().median()
    F2 = next_otm_op_info['F'].dropna().median()

    strike_arr = curr_otm_op_info.strike.values
    delta_ki = np.concatenate((np.array([strike_arr[1] - strike_arr[0]]), (strike_arr[2:] - strike_arr[:-2]) * 0.5,
                               np.array([strike_arr[-1] - strike_arr[-2]])))
    curr_otm_op_info['delta_ki'] = delta_ki

    strike_arr = next_otm_op_info.strike.values
    delta_ki = np.concatenate((np.array([strike_arr[1] - strike_arr[0]]), (strike_arr[2:] - strike_arr[:-2]) * 0.5,
                               np.array([strike_arr[-1] - strike_arr[-2]])))
    next_otm_op_info['delta_ki'] = delta_ki

    first_term_1 = (2 / T1) * np.sum((curr_otm_op_info.delta_ki.values / curr_otm_op_info.strike.values ** 2) * np.exp(
        R * T1) * curr_otm_op_info.price.values)

    second_term_1 = (1 / T1) * (F1 / etf_price - 1) ** 2

    sigma_sqr_1 = first_term_1 - second_term_1

    first_term_2 = (2 / T2) * np.sum((next_otm_op_info.delta_ki.values / next_otm_op_info.strike.values ** 2) * np.exp(
        R * T2) * next_otm_op_info.price.values)

    second_term_2 = (1 / T2) * (F2 / etf_price - 1) ** 2

    sigma_sqr_2 = first_term_2 - second_term_2

    N_T1 = int(T1 * (365 * 24 * 60))
    N_T2 = int(T2 * (365 * 24 * 60))
    N_30 = 43200
    N_365 = 525600

    frac1 = (N_T2 - N_30) / (N_T2 - N_T1)
    frac2 = (N_30 - N_T1) / (N_T2 - N_T1)

    vix = 100 * ((T1 * sigma_sqr_1 * frac1 + T2 * sigma_sqr_2 * frac2) * (N_365 / N_30)) ** 0.5

    return vix


def main(underlying):
    vixname = underlying_dict[underlying]
    vixkey = f"ZQ:VIX:sig:{vixname}"
    time_list = []
    vix_list = []

    curr_time = datetime.datetime.now()

    preprare_intial_info_time = datetime.datetime(curr_time.year, curr_time.month, curr_time.day, 9, 0, 0, 0)
    openning_auction_time = datetime.datetime(curr_time.year, curr_time.month, curr_time.day, 9, 25, 5, 0)
    noon_closing_time = datetime.datetime(curr_time.year, curr_time.month, curr_time.day, 11, 30, 0, 0)
    noon_openning_time = datetime.datetime(curr_time.year, curr_time.month, curr_time.day, 13, 0, 0, 0)
    closing_time = datetime.datetime(curr_time.year, curr_time.month, curr_time.day, 15, 0, 30, 0)

    op_info, op_pair, exp_dts = None, None, None
    whether_intitial_op_info = False

    while 1:

        try:

            curr_time = datetime.datetime.now()

            if not whether_intitial_op_info:
                if curr_time > preprare_intial_info_time:
                    op_info, op_pair, exp_dts = get_preopen_op_info()
                    whether_intitial_op_info = True
                    print('Options Contracts Info Ready!')

            if (curr_time >= openning_auction_time and curr_time <= noon_closing_time) or \
                    (curr_time >= noon_openning_time and curr_time <= closing_time):

                vix = get_vix(op_info, op_pair, exp_dts)
                time_list.append(str(curr_time))
                vix_list.append(vix)

                rd170.set(f"{vixkey}", vix)
                rd170.publish(vixkey, str(vix))
                curtvtimestr = f'H:{datetime.datetime.now().strftime("%H%M%S")}'
                rd181.hset(curtvtimestr, vixkey, vix)

                print('time: ', datetime.datetime.now(), ' | ', underlying, 'vix: ', '{:.5f}'.format(vix))

                curr_time_float = time.time()
                next_time = int(curr_time_float + 1)
                sleep_amt = next_time - curr_time_float
                time.sleep(sleep_amt)
            else:
                print(datetime.datetime.now(), 'Waiting for Market Open!')
                curr_time_float = time.time()
                next_time = int(curr_time_float + 1)
                sleep_amt = next_time - curr_time_float
                time.sleep(sleep_amt)

            if curr_time > closing_time:
                date = str(curr_time).split()[0]
                vix_df = pd.DataFrame(vix_list)
                vix_df.columns = ['vix']
                vix_df.index = time_list
                # vix_df.to_csv('/A/MD/vix_data/vix_live_history/'+vixname+'ETF/'+date+'.csv')
                break

        except:

            print('###################### FATAL ERROR!!! ######################')



if __name__ == '__main__':
    from argparse import ArgumentParser
    parser = ArgumentParser('可以添加以下变量')
    parser.add_argument('-O', '--opkind', type=str, default='510050', help='输入opkind，默认是50')
    args = parser.parse_args()
    underlying = args.opkind
    main(underlying)