from nb_common import *
import copy
from tqdm import tqdm
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
import talib as ta
from ut_redis import FrRedis
jq_auth()


today = datetime.datetime.now().date()

rd170 = FrRedis("************")

f_ovol = eval(rd170.get('ETC:app:risk_ctrl:f_ovol'))
f_ovol_std = eval(rd170.get('ETC:app:risk_ctrl:f_ovol_std'))

curr_multiplier = {
    'IH': f_ovol['510050'] / f_ovol_std['510050'],
    'IF': f_ovol['510300'] / f_ovol_std['510300'],
    'IC': f_ovol['510500'] / f_ovol_std['510500'],
    'IM': f_ovol['512100'] / f_ovol_std['512100'],
}

# curr_multiplier = {
#     'IH': 29.33/30,
#     'IF': 1,
#     'IC': 1,
#     'IM': 2.5,
# }

def get_all_tradedays(starting_date, ending_date):
    all_t_days = pickle_read_tdays()
    if datetime.datetime.now().date() in all_t_days:
        all_t_days = all_t_days[:-1]

    all_t_days = all_t_days[(all_t_days >= starting_date) & (all_t_days <= ending_date)]

    return all_t_days


def get_all_op_ex_dates():
    full_info = pd.read_pickle(f'{MDLSTDIR}/all_securities/full.pickle')
    all_options_ex_dates = sorted(
        full_info[(full_info.type == 'options') & (full_info.name.str[:2] == '10')].end_date.dt.strftime(
            "%Y-%m-%d").unique())
    all_options_ex_dates = np.array(
        [datetime.datetime.strptime(date, "%Y-%m-%d").date() for date in all_options_ex_dates])

    return all_options_ex_dates


def get_all_fu_ex_dates():
    full_info = pd.read_pickle(f'{MDLSTDIR}/all_securities/full.pickle')
    all_futures_ex_dates = sorted(
        full_info[(full_info.type == 'futures') & (full_info.name.str[:2] == 'IH')].end_date.dt.strftime(
            "%Y-%m-%d").unique())[:-1]
    all_futures_ex_dates = np.array(
        [datetime.datetime.strptime(date, "%Y-%m-%d").date() for date in all_futures_ex_dates])

    return all_futures_ex_dates



def get_all_etf_dividend_info():
    op_kinds = [
        '510050', '510300', '510500', '159922', '159919', '588000', '588080', '159915', \
        '510100', '510310', '510330', '159949', '512100',
    ]
    all_dividend_info = []
    q = jq.query(jq.finance.FUND_DIVIDEND).filter(
        (jq.finance.FUND_DIVIDEND.ex_date >= '2020-01-01') & (jq.finance.FUND_DIVIDEND.code.in_(op_kinds))) \
        .order_by(jq.finance.FUND_DIVIDEND.ex_date).limit(5000)
    all_dividend_info = jq.finance.run_query(q)
    all_dividend_info = all_dividend_info[all_dividend_info.event.str.contains('分红')]

    # return all_dividend_info[(all_dividend_info.code == op_kind)]
    return all_dividend_info


all_fu_ex_dates = get_all_fu_ex_dates()
all_op_ex_dates = get_all_op_ex_dates()

all_dividend_info = get_all_etf_dividend_info()


def get_zero(f_kind, f_exp, date, zero_multiplier=1):
    all_t_days = pickle_read_tdays()
    if datetime.datetime.now().date() in all_t_days:
        all_t_days = all_t_days[:-1]

    next_fu_ex_date = np.sort(all_fu_ex_dates[(all_fu_ex_dates >= date)])[f_exp]

    f_kind_dict = {'IH': ('510050', '000016', '50ETF', 1), 'IF': ('510300', '000300', '300ETF', 1),
                   'IC': ('510500', '000905', '500ETF', 1), 'IM': ('512100', '000852', '1000ETF', 1)}

    daily_st_timestamp, daily_end_timestamp = datetime.datetime(date.year, date.month, date.day, 9, 0,
                                                                0), datetime.datetime(date.year, date.month, date.day,
                                                                                      15, 0, 0)
    timestamps = pickle_read_md1m_s(f_kind_dict[f_kind][0]).loc[daily_st_timestamp:daily_end_timestamp, :].index

    seclst_df = pickle_read_seclst(str(date))

    f_exp_lst = sorted(
        seclst_df[(seclst_df.name.str.startswith(f_kind + '2')) & (seclst_df.type == 'futures')].end_date.unique())
    f_exp = f_exp_lst[f_exp]
    exp_dt = f_exp

    div_info = all_dividend_info[all_dividend_info.code == f_kind_dict[f_kind][0]]
    div_info = div_info[(div_info.ex_date > date) & (div_info.ex_date <= next_fu_ex_date)]
    if div_info.shape[0] > 0:
        adj_pts = div_info.proportion.sum() * 1e3
    else:
        adj_pts = 0

    if exp_dt.date() > all_t_days[-1]:
        curr_date = all_t_days[-1]
        daily_st_timestamp = datetime.datetime(curr_date.year, curr_date.month, curr_date.day, 9, 0, 0)
        daily_end_timestamp = datetime.datetime(curr_date.year, curr_date.month, curr_date.day, 15, 0, 0)
        index_prices = pickle_read_md1m_i(f_kind_dict[f_kind][1]).loc[daily_st_timestamp:daily_end_timestamp, :].close
        etf_prices = (pickle_read_md1m_s(f_kind_dict[f_kind][0]).loc[daily_st_timestamp:daily_end_timestamp, :].close * 1e3 + adj_pts)  * zero_multiplier
        zero = pd.Series([etf_prices.iloc[-1] * f_kind_dict[f_kind][3] - index_prices.iloc[-1]] * index_prices.shape[0], index=timestamps)
    else:
        daily_st_timestamp = datetime.datetime(exp_dt.year, exp_dt.month, exp_dt.day, 9, 0, 0)
        daily_end_timestamp = datetime.datetime(exp_dt.year, exp_dt.month, exp_dt.day, 15, 0, 0)
        index_prices = pickle_read_md1m_i(f_kind_dict[f_kind][1]).loc[daily_st_timestamp:daily_end_timestamp, :].close
        etf_prices = (pickle_read_md1m_s(f_kind_dict[f_kind][0]).loc[daily_st_timestamp:daily_end_timestamp, :].close * 1e3 + adj_pts) * zero_multiplier
        zero = pd.Series([etf_prices.iloc[-1] * f_kind_dict[f_kind][3] - index_prices.iloc[-1]] * index_prices.shape[0], index=timestamps)

    return zero





def get_month(op_only=False, f_only=False, op_kind=None, f_kind=None, date=None):
    op_kind_dict = {'510050': '50ETF', '510300': '300ETF', '510500': '500ETF', '159919': '300ETF', '159922': '500ETF',
                    '588000': '科创50', '588080': '科创50', '159915': '创业板'}

    seclst_df = pickle_read_seclst(str(date))

    if f_only and not op_only:
        f_exp_lst = sorted(
            seclst_df[(seclst_df.name.str.startswith(f_kind + '2')) & (seclst_df.type == 'futures')].end_date.unique())
        exp_dt = f_exp_lst[0]
    elif not f_only and op_only:
        op_exp_lst = sorted(seclst_df[(seclst_df.display_name.str.contains(op_kind_dict[op_kind])) & (
                    seclst_df.type == 'options')].end_date.unique())
        exp_dt = op_exp_lst[0]
    elif not f_only and not op_only:
        op_exp_lst = sorted(seclst_df[(seclst_df.display_name.str.contains(op_kind_dict[op_kind])) & (
                    seclst_df.type == 'options')].end_date.unique())
        f_exp_lst = sorted(
            seclst_df[(seclst_df.name.str.startswith(f_kind + '2')) & (seclst_df.type == 'futures')].end_date.unique())
        op_exp, f_exp = op_exp_lst[0], f_exp_lst[0]
        exp_dt = min(op_exp, f_exp)

    dt = ''.join(str(exp_dt).split()[0].split('-')[:2])[2:]

    return dt



def cross_month(
        strat,
        type='abs',
        whether_normalize=True,
        st_mon=None,
        ed_mon=None,
        st_dt=None,
        ed_dt=None,
        end_truncate_period=0,
):
    strat = copy.deepcopy(strat)

    all_mon_ = np.array(sorted(list(strat[type].keys())))

    st_mon_idx = np.argwhere(all_mon_ == str(st_mon))[0][0] if str(st_mon) in all_mon_ and st_mon is not None else 0
    ed_mon_idx = np.argwhere(all_mon_ == str(ed_mon))[0][0] if str(ed_mon) in all_mon_ and ed_mon is not None else int(
        1e6)

    all_mon = all_mon_[st_mon_idx:ed_mon_idx + 1]

    month_ts = None

    cross_mon = []
    for mon in all_mon:

        if month_ts is None:
            daily_ts = [str(t).split()[1] for t in strat[type][mon].index[:240]]
            month_ts = ['day ' + str(d) + ' ' + t for d in range(25) for t in daily_ts]

        mon_data = strat[type][mon].values.flatten()
        mon_data = np.concatenate((np.repeat(mon_data[0], 6000 - mon_data.shape[0]), mon_data))

        if st_dt is None and ed_dt is not None:
            mon_data = mon_data[:int(ed_dt * 240)]
        elif st_dt is not None and ed_dt is None:
            mon_data = mon_data[int(st_dt * 240):]
        elif st_dt is not None and ed_dt is not None:
            mon_data = mon_data[int(st_dt * 240):int(ed_dt * 240)]

        if whether_normalize:
            mon_data -= (mon_data[:60]).mean()

        if end_truncate_period > 0:
            mon_data = mon_data[:-1 * end_truncate_period]

        cross_mon.append(mon_data.reshape(-1, 1))

    cross_mon = np.mean(np.concatenate(cross_mon, axis=1), axis=1)

    fig = make_subplots(rows=1, cols=1, shared_xaxes=True, vertical_spacing=0.05, specs=[[{'secondary_y': False}]], \
                        subplot_titles=('Cross Month Performance Plot', 'Plot'))

    fig.add_trace(go.Scatter(x=month_ts, y=cross_mon, name='Strategy'), row=1, col=1, secondary_y=False)

    fig.update_layout(width=2000,
                      height=1200,
                      xaxis_rangeslider_visible=False,
                      showlegend=False,
                      title_text=('<b> Cross Month Performance Strategy </b>')
                      )

    fig.update_traces(xaxis="x1")

    fig.update_xaxes(showspikes=True,
                     spikemode='across',
                     spikesnap='cursor',
                     spikecolor='gray',
                     spikethickness=0.8,
                     title_text="Time",
                     type='category',
                     nticks=5,
                     row=1, col=1
                     )
    fig.update_yaxes(showspikes=True,
                     spikemode='across',
                     spikesnap='cursor',
                     spikecolor='gray',
                     spikethickness=0.8,
                     row=1, col=1
                     )

    fig.update_yaxes(title_text="Strategy", row=1, col=1, secondary_y=False)

    fig.show()



def get_multi_plots_stack(
        strat,
        type,
        st_mon=None,
        ed_mon=None,
        st_dt=None,
        ed_dt=None,
        end_truncate_period=120,
        whether_normalize=False,
        abnormal_direc='>',
        abnormal_threshold=5,
        ylim=None
):
    def convert_x_axis(data):
        all_index = data.index
        dts = ['day ' + str(idx) for idx in range(np.unique([str(tick.date()) for tick in all_index]).shape[0])]
        dt_time = [str(dts[int(idx / 240)]) + ' ' + str(tick.time()) for idx, tick in enumerate(all_index)]
        return dt_time

    fig = make_subplots(rows=1, cols=1, shared_xaxes=True, vertical_spacing=0.05, specs=[[{'secondary_y': False}]], \
                        subplot_titles=('', 'Plot'))

    strat = copy.deepcopy(strat)

    all_mon_ = np.array(sorted(list(strat[type].keys())))

    st_mon_idx = np.argwhere(all_mon_ == str(st_mon))[0][0] if str(st_mon) in all_mon_ and st_mon is not None else 0
    ed_mon_idx = np.argwhere(all_mon_ == str(ed_mon))[0][0] if str(ed_mon) in all_mon_ and ed_mon is not None else int(
        1e6)

    all_mon = all_mon_[st_mon_idx:ed_mon_idx + 1]

    for the_mon in tqdm(all_mon):

        the_mon_data = strat[type][the_mon]

        if st_dt is None and ed_dt is not None:
            the_mon_data = the_mon_data.iloc[:int(ed_dt * 240)]
        elif st_dt is not None and ed_dt is None:
            the_mon_data = the_mon_data.iloc[int(st_dt * 240):]
        elif st_dt is not None and ed_dt is not None:
            the_mon_data = the_mon_data.iloc[int(st_dt * 240):int(ed_dt * 240)]

        if end_truncate_period > 0 and ed_dt is None:
            the_mon_data = the_mon_data.iloc[:int(-1 * end_truncate_period)]

        if whether_normalize:
            the_mon_data -= the_mon_data.iloc[:120].mean()

        fig.add_trace(go.Scatter(x=convert_x_axis(the_mon_data), y=the_mon_data.values.flatten(),
                                 name='Strategy ' + str(the_mon)), row=1, col=1, secondary_y=False)

        if abnormal_threshold:
            if abnormal_direc == '>':
                if np.quantile(the_mon_data.values, 0.99) > abnormal_threshold:
                    print('Abnormal Month: ', the_mon, ' | 99% Quantile: ', str(np.quantile(the_mon_data.values, 0.99)))
            else:
                if np.quantile(the_mon_data.values, 0.01) < abnormal_threshold:
                    print('Abnormal Month: ', the_mon, str(np.quantile(the_mon_data.values, 0.01)))

    fig.update_layout(width=2000,
                      height=1200,
                      xaxis_rangeslider_visible=False,
                      showlegend=False,
                      title_text=('<b> Stacked Plots By Month </b>')
                      )

    fig.update_traces(xaxis="x1")

    fig.update_xaxes(showspikes=True,
                     spikemode='across',
                     spikesnap='cursor',
                     spikecolor='gray',
                     spikethickness=0.8,
                     title_text="Time",
                     type='category',
                     nticks=5,
                     row=1, col=1
                     )
    fig.update_yaxes(showspikes=True,
                     spikemode='across',
                     spikesnap='cursor',
                     spikecolor='gray',
                     spikethickness=0.8,
                     row=1, col=1
                     )
    if ylim is not None:
        fig.update_yaxes(title_text="Strategy", row=1, col=1, secondary_y=False, range=ylim)
    else:
        fig.update_yaxes(title_text="Strategy", row=1, col=1, secondary_y=False)

    fig.show()



def get_multi_plots_parallel(
        strat,
        type,
        st_mon=None,
        ed_mon=None,
        st_dt=None,
        ed_dt=None,
        end_truncate_period=120,
        whether_normalize=False,
        ylim=None
):
    fig = make_subplots(rows=1, cols=1, shared_xaxes=True, vertical_spacing=0.05, specs=[[{'secondary_y': True}]], \
                        subplot_titles=('', 'Plot'))

    strat = copy.deepcopy(strat)

    all_mon_ = np.array(sorted(list(strat[type].keys())))

    st_mon_idx = np.argwhere(all_mon_ == str(st_mon))[0][0] if str(st_mon) in all_mon_ and st_mon is not None else 0
    ed_mon_idx = np.argwhere(all_mon_ == str(ed_mon))[0][0] if str(ed_mon) in all_mon_ and ed_mon is not None else int(
        1e6)

    all_mon = all_mon_[st_mon_idx:ed_mon_idx + 1]

    all_mon_data = []
    all_b_data = []
    for mon in tqdm(all_mon):

        the_mon_data = strat[type][mon]
        b_data = strat['b'][mon]

        if st_dt is None and ed_dt is not None:
            the_mon_data = the_mon_data.iloc[:int(ed_dt * 240)]
            b_data = b_data.iloc[:int(ed_dt * 240)]
        elif st_dt is not None and ed_dt is None:
            the_mon_data = the_mon_data.iloc[int(st_dt * 240):]
            b_data = b_data.iloc[int(st_dt * 240):]
        elif st_dt is not None and ed_dt is not None:
            the_mon_data = the_mon_data.iloc[int(st_dt * 240):int(ed_dt * 240)]
            b_data = b_data.iloc[int(st_dt * 240):int(ed_dt * 240)]

        if end_truncate_period > 0 and ed_dt is None:
            the_mon_data = the_mon_data.iloc[:int(-1 * end_truncate_period)]
            b_data = b_data.iloc[:int(-1 * end_truncate_period)]

        if whether_normalize:
            the_mon_data -= the_mon_data.iloc[:120].mean()

        all_mon_data.append(the_mon_data)
        all_b_data.append(b_data)

    all_mon_data = pd.concat(all_mon_data)
    all_b_data = pd.concat(all_b_data)

    fig.add_trace(go.Scatter(x=all_mon_data.index.astype(str), y=all_mon_data.values.flatten(), name='Strategy'), row=1,
                  col=1, secondary_y=False)
    fig.add_trace(go.Scatter(x=all_b_data.index.astype(str), y=all_b_data.values.flatten(),
                             line=dict(color='rgba(178, 34, 34, 0.5)'), name='Beta'), row=1, col=1, secondary_y=True)

    fig.update_layout(width=2000,
                      height=1200,
                      xaxis_rangeslider_visible=False,
                      showlegend=False,
                      title_text=('<b> Combined Plots By Month </b>')
                      )

    fig.update_traces(xaxis="x1")

    fig.update_xaxes(showspikes=True,
                     spikemode='across',
                     spikesnap='cursor',
                     spikecolor='gray',
                     spikethickness=0.8,
                     title_text="Time",
                     type='category',
                     nticks=5,
                     row=1, col=1
                     )
    fig.update_yaxes(showspikes=True,
                     spikemode='across',
                     spikesnap='cursor',
                     spikecolor='gray',
                     spikethickness=0.8,
                     row=1, col=1
                     )
    if ylim is not None:
        fig.update_yaxes(title_text="Strategy", row=1, col=1, secondary_y=False, range=ylim)
    else:
        fig.update_yaxes(title_text="Strategy", row=1, col=1, secondary_y=False)

    fig.update_yaxes(title_text="Beta", row=1, col=1, secondary_y=True)

    fig.show()


def get_pair_plot(strat, type, mon, st_dt=None, ed_dt=None, end_truncate_period=120, ylim=None, rolling_window=10,
                  show_legend=True,
                  bbands_params={
                      'show': True,
                      'timeperiod': 20,
                      'std': 2,
                      'matype': 1,
                  },
                  ):
    fig = make_subplots(rows=1, cols=1, shared_xaxes=True, vertical_spacing=0.05, specs=[[{'secondary_y': True}]], \
                        subplot_titles=('', 'Plot'))
    mon = str(mon)

    mon_data = strat[type][mon]

    b_data = strat['b'][mon]

    if st_dt is None and ed_dt is not None:
        mon_data = mon_data.iloc[:int(ed_dt * 240)]
        b_data = b_data.iloc[:int(ed_dt * 240)]
    elif st_dt is not None and ed_dt is None:
        mon_data = mon_data.iloc[int(st_dt * 240):]
        b_data = b_data.iloc[int(st_dt * 240):]
    elif st_dt is not None and ed_dt is not None:
        mon_data = mon_data.iloc[int(st_dt * 240):int(ed_dt * 240)]
        b_data = b_data.iloc[int(st_dt * 240):int(ed_dt * 240)]

    if end_truncate_period > 0 and ed_dt is None:
        b_data = b_data.iloc[:int(-1 * end_truncate_period)]
        mon_data = mon_data.iloc[:int(-1 * end_truncate_period)]

    mon_data_ma = ta.EMA(mon_data, rolling_window)

    up_band, mid_band, dn_band = ta.BBANDS(mon_data_ma, timeperiod=bbands_params['timeperiod'],
                                           nbdevup=bbands_params['std'], nbdevdn=bbands_params['std'],
                                           matype=bbands_params['matype'])

    if bbands_params['show']:
        fig.add_trace(go.Scatter(x=mon_data.index.astype(str), y=up_band.values.flatten(), name='boll_up',
                                 line=dict(color='rgba(20, 140, 25, 0.8)')), row=1, col=1, secondary_y=False)
        fig.add_trace(go.Scatter(x=mon_data.index.astype(str), y=dn_band.values.flatten(), name='boll_dn',
                                 line=dict(color='rgba(20, 140, 25, 0.8)')), row=1, col=1, secondary_y=False)

    fig.add_trace(go.Scatter(x=mon_data.index.astype(str), y=mon_data.values.flatten(), name='Strategy',
                             line=dict(color='rgba(39, 118, 245, 1)')), row=1, col=1, secondary_y=False)
    fig.add_trace(go.Scatter(x=mon_data_ma.index.astype(str), y=mon_data_ma.values.flatten(), name='EMA',
                             line=dict(color='rgba(20, 140, 25, 0.8)')), row=1, col=1, secondary_y=False)

    fig.add_trace(go.Scatter(x=b_data.index.astype(str), y=b_data.values.flatten(), name='Beta',
                             line=dict(color='rgba(178, 34, 34, 0.5)')), \
                  row=1, col=1, secondary_y=True)

    fig.update_layout(width=2000,
                      height=1200,
                      xaxis_rangeslider_visible=False,
                      showlegend=show_legend,
                      title_text=('<b>' + str(mon) + ' Strategy & Beta </b>')
                      )

    fig.update_traces(xaxis="x1")

    fig.update_xaxes(showspikes=True,
                     spikemode='across',
                     spikesnap='cursor',
                     spikecolor='gray',
                     spikethickness=0.8,
                     title_text="Time",
                     type='category',
                     nticks=5,
                     row=1, col=1
                     )
    fig.update_yaxes(showspikes=True,
                     spikemode='across',
                     spikesnap='cursor',
                     spikecolor='gray',
                     spikethickness=0.8,
                     row=1, col=1
                     )
    if ylim is not None:
        fig.update_yaxes(title_text="Strategy", row=1, col=1, secondary_y=False, range=ylim)
    else:
        fig.update_yaxes(title_text="Strategy", row=1, col=1, secondary_y=False)
    fig.update_yaxes(title_text="Beta", row=1, col=1, secondary_y=True)

    fig.show()


def get_segment_stats(
        strat,
        type,
        st_mon=None,
        ed_mon=None,
        st_dt=None,
        ed_dt=None,
        end_truncate_period=120,
        whether_normalize=True,
        top_threshold=None,
        bot_threshold=None,
):
    strat = copy.deepcopy(strat)

    all_mon_ = np.array(sorted(list(strat[type].keys())))

    st_mon_idx = np.argwhere(all_mon_ == str(st_mon))[0][0] if str(st_mon) in all_mon_ and st_mon is not None else 0
    ed_mon_idx = np.argwhere(all_mon_ == str(ed_mon))[0][0] if str(ed_mon) in all_mon_ and ed_mon is not None else int(
        1e6)

    all_mon = all_mon_[st_mon_idx:ed_mon_idx]

    top_counter = 0
    bot_counter = 0
    counter = 0

    vals = []
    for mon in all_mon:

        seg_data = strat[type][mon].values

        if st_dt is None and ed_dt is not None:
            seg_data = seg_data[:int(240 * ed_dt)]
        elif st_dt is not None and ed_dt is None:
            seg_data = seg_data[int(240 * st_dt):]
        elif st_dt is not None and ed_dt is not None:
            seg_data = seg_data[int(240 * st_dt):int(240 * ed_dt)]

        if end_truncate_period > 0 and ed_dt is None:
            seg_data = seg_data[:int(-1 * end_truncate_period)]

        if seg_data.shape[0] > 0:
            if whether_normalize:
                seg_data = seg_data - seg_data[:120].mean()
                vals.append(seg_data)
            else:
                vals.append(seg_data)

        counter += 1
        if top_threshold:
            if np.quantile(seg_data, 0.95) >= top_threshold:
                top_counter += 1
            else:
                print('95% Quantile:', round(np.quantile(seg_data, 0.95), 2))
        if bot_threshold:
            if np.quantile(seg_data, 0.05) <= bot_threshold:
                bot_counter += 1
            else:
                print('5% Quantile:', round(np.quantile(seg_data, 0.05), 2))

    if top_threshold:
        print(str(round(top_counter / counter * 100, 1)) + '%', 'Reached Top Threshold!')
    if bot_threshold:
        print(str(round(bot_counter / counter * 100, 1)) + '%', 'Reached Bottom Threshold!')

    vals = np.concatenate(vals)

    print('Mean:', round(np.mean(vals), 3), 'Median:', round(np.median(vals), 3), \
          '90% QUantile: (', round(np.quantile(vals, 0.1), 3), round(np.quantile(vals, 0.9), 3), ') Quantile 95%: (', \
          round(np.quantile(vals, 0.05), 3), round(np.quantile(vals, 0.95), 3), ') Quantile 99%: (', \
          round(np.quantile(vals, 0.01), 3), round(np.quantile(vals, 0.99), 3), ')')

    fig = go.Figure(data=[go.Histogram(x=vals)])
    fig.update_layout(
        width=1200,
        height=900,
        title='<b> Strategy Value Histogram</b>',
        xaxis_title='Strategy Values',
        yaxis_title='Count',
        bargap=0.1
    )
    fig.show()


def get_arbitrage_strat(strat1, strat2, multiplier1=1, multiplier2=1):
    strat1 = copy.deepcopy(strat1)
    strat2 = copy.deepcopy(strat2)

    arb = {'abs': {}, 'b': {}}
    for mon in strat1['abs'].keys():
        if mon in strat2['abs']:
            arb['abs'][mon] = strat1['abs'][mon] * multiplier1 - strat2['abs'][mon] * multiplier2
            arb['b'][mon] = strat1['b'][mon] * multiplier1 - strat2['b'][mon] * multiplier2

    if 'rel' in strat1 and 'rel' in strat2:
        for mon in strat1['rel'].keys():
            if mon in strat2['rel']:
                arb['rel'][mon] = strat1['rel'][mon] - strat2['rel'][mon]
    return arb



def get_daily_a5(op_kind, etf_kind, op_exp, date, op_multiplier=1, etf_multiplier=1):
    mdi = pickle_read_mdi(str(date))
    etf_price = pickle_read_md1m_s(op_kind).loc[date:datetime.datetime.combine(date, datetime.time(15)), 'avg']
    time_stamps = etf_price.index

    curr_month = mdi.opkind[op_kind]['a_yymm'][op_exp]
    all_k = np.array(mdi.opkind[op_kind]['d_yymm_pxu_M'][curr_month])
    all_k_arr = np.array([int(con[1:]) for con in all_k])
    close_k = all_k[np.argsort(np.abs(all_k_arr - etf_price.mean() * 1e3))][:3]
    close_k_arr = all_k_arr[np.argsort(np.abs(all_k_arr - etf_price.mean() * 1e3))][:3]

    call_arr = np.array([mdi.oplstt.xs((op_kind, curr_month, "M", k)).C for k in close_k])
    put_arr = np.array([mdi.oplstt.xs((op_kind, curr_month, "M", k)).P for k in close_k])

    call_prices = np.concatenate([pickle_read_md1m_o(con).loc[str(date)].avg.values.reshape(-1, 1) for con in call_arr],
                                 axis=1)
    put_prices = np.concatenate([pickle_read_md1m_o(con).loc[str(date)].avg.values.reshape(-1, 1) for con in put_arr],
                                axis=1)

    strikes = np.repeat(close_k_arr.reshape(1, -1), repeats=call_prices.shape[0], axis=0) * 10

    mean_syn_forward = (call_prices - put_prices + strikes).mean(axis=1) * 0.1

    if op_kind != etf_kind:
        etf_price = pickle_read_md1m_s(etf_kind).loc[date:datetime.datetime.combine(date, datetime.time(15)), 'avg']

    a5 = pd.Series(mean_syn_forward * op_multiplier - etf_price.values * 1e3 * etf_multiplier, index=time_stamps)

    b = pd.Series(mean_syn_forward, index=time_stamps)

    # if date == datetime.date(2024, 5, 16) and op_kind == '510500':
    #     a5 += 87

    return a5, b


def get_daily_a5_xie(op_kind, etf_kind, op_exp, date, op_multiplier=1, etf_multiplier=1):
    etf_price = pickle_read_md1m_s(op_kind).loc[date:datetime.datetime.combine(date, datetime.time(15)), 'avg']
    time_stamps = etf_price.index

    def get_opcode(date, underlying, mon, strike, cp, t):
        mdi = pickle_read_mdi(str(date))
        und_price = \
        pickle_read_md1m_s(underlying).loc[date:datetime.datetime.combine(date, datetime.time(15)), 'avg'].iloc[t]

        exp_dts = mdi.opkind[underlying]['a_yymm']

        strikes = {exp_dt: np.array(mdi.opkind[underlying]['d_yymm_pxu_M'][exp_dt]) for exp_dt in exp_dts}
        atm_idx = {exp_dt: np.argmin(np.abs(np.array([int(k[1:]) * 1e-3 for k in strikes[exp_dt]]) - und_price)) for
                   exp_dt in strikes.keys()}
        strike_price = strikes[exp_dts[mon]][
            np.clip(atm_idx[exp_dts[mon]] + strike, 0, strikes[exp_dts[mon]].shape[0] - 1)]

        op_code = mdi.oplstt.xs((underlying, exp_dts[mon], "M", strike_price))[cp]

        return op_code, int(strike_price[2:])

    otm_c, otm_k = get_opcode(date=date, underlying=op_kind, mon=0, strike=2, cp='C', t=0)
    atm_p, atm_k = get_opcode(date=date, underlying=op_kind, mon=0, strike=1, cp='P', t=0)
    if atm_k > etf_price[0]:
        otm_c, otm_k = get_opcode(date=date, underlying=op_kind, mon=0, strike=1, cp='C', t=0)
        atm_p, atm_k = get_opcode(date=date, underlying=op_kind, mon=0, strike=0, cp='P', t=0)

    call_price = pickle_read_md1m_o(otm_c).loc[str(date)].avg.values
    put_price = pickle_read_md1m_o(atm_p).loc[str(date)].avg.values

    strikes = np.repeat(atm_k, call_price.shape[0]) * 10

    syn_forward = (call_price - put_price + strikes) * 0.1

    a5 = pd.Series(syn_forward - etf_price.values * 1e3, index=time_stamps)

    b = pd.Series(etf_price.values * 1e3, index=time_stamps)

    return a5, b


def get_daily_a51(op_kind, f_kind, op_exp, f_exp, date, zero_multiplier=1):
    mdi = pickle_read_mdi(str(date))
    etf_price = pickle_read_md1m_s(op_kind).loc[date:datetime.datetime.combine(date, datetime.time(15)), 'avg']
    time_stamps = etf_price.index

    curr_month = mdi.opkind[op_kind]['a_yymm'][op_exp]
    all_k = np.array(mdi.opkind[op_kind]['d_yymm_pxu_M'][curr_month])
    all_k_arr = np.array([int(con[1:]) for con in all_k])
    close_k = all_k[np.argsort(np.abs(all_k_arr - etf_price.mean() * 1e3))][:3]
    close_k_arr = all_k_arr[np.argsort(np.abs(all_k_arr - etf_price.mean() * 1e3))][:3]

    call_arr = np.array([mdi.oplstt.xs((op_kind, curr_month, "M", k)).C for k in close_k])
    put_arr = np.array([mdi.oplstt.xs((op_kind, curr_month, "M", k)).P for k in close_k])

    call_prices = np.concatenate([pickle_read_md1m_o(con).loc[str(date)].avg.values.reshape(-1, 1) for con in call_arr],
                                 axis=1)
    put_prices = np.concatenate([pickle_read_md1m_o(con).loc[str(date)].avg.values.reshape(-1, 1) for con in put_arr],
                                axis=1)

    strikes = np.repeat(close_k_arr.reshape(1, -1), repeats=call_prices.shape[0], axis=0) * 10

    mean_syn_forward = (call_prices - put_prices + strikes).mean(axis=1) * 0.1

    curr_month_f = mdi.fkind[f_kind]['a_icode'][f_exp]

    future_price = pickle_read_md1m_f(curr_month_f).loc[str(date)].avg.values

    a51 = pd.Series(mean_syn_forward * zero_multiplier - future_price, index=time_stamps)

    b = pd.Series(mean_syn_forward, index=time_stamps)

    return a51, b




def get_daily_a13(etf_kind, f_kind, f_exp, date, zero_multiplier=1):
    mdi = pickle_read_mdi(str(date))
    etf_price = pickle_read_md1m_s(etf_kind).loc[date:datetime.datetime.combine(date, datetime.time(15)), 'avg'] * 1e3
    time_stamps = etf_price.index

    curr_month_f = mdi.fkind[f_kind]['a_icode'][f_exp]

    future_price = pickle_read_md1m_f(curr_month_f).loc[str(date)].avg.values

    a13 = pd.Series(future_price - etf_price * zero_multiplier, index=time_stamps)

    b = pd.Series(etf_price, index=time_stamps)

    return a13, b




def get_daily_ff(f_kind, f_exp1, f_exp2, date):
    mdi = pickle_read_mdi(str(date))

    curr_month_f = mdi.fkind[f_kind]['a_icode'][f_exp1]
    next_month_f = mdi.fkind[f_kind]['a_icode'][f_exp2]

    curr_future_price = pickle_read_md1m_f(curr_month_f).loc[str(date)].avg
    next_future_price = pickle_read_md1m_f(next_month_f).loc[str(date)].avg

    ff = (curr_future_price - next_future_price) * (-1)

    b = curr_future_price

    return ff, b


def get_daily_opop(op_kind, op_exp1, op_exp2, date):
    mdi = pickle_read_mdi(str(date))
    etf_price = pickle_read_md1m_s(op_kind).loc[date:datetime.datetime.combine(date, datetime.time(15)), 'avg']
    timestamps = etf_price.index

    curr_month = mdi.opkind[op_kind]['a_yymm'][op_exp1]
    all_k = np.array(mdi.opkind[op_kind]['d_yymm_pxu_M'][curr_month])
    all_k_arr = np.array([int(con[1:]) for con in all_k])
    close_k = all_k[np.argsort(np.abs(all_k_arr - etf_price.mean() * 1e3))][:3]
    close_k_arr = all_k_arr[np.argsort(np.abs(all_k_arr - etf_price.mean() * 1e3))][:3]

    call_arr = np.array([mdi.oplstt.xs((op_kind, curr_month, "M", k)).C for k in close_k])
    put_arr = np.array([mdi.oplstt.xs((op_kind, curr_month, "M", k)).P for k in close_k])

    call_prices = np.concatenate([pickle_read_md1m_o(con).loc[str(date)].avg.values.reshape(-1, 1) for con in call_arr],
                                 axis=1)
    put_prices = np.concatenate([pickle_read_md1m_o(con).loc[str(date)].avg.values.reshape(-1, 1) for con in put_arr],
                                axis=1)

    strikes = np.repeat(close_k_arr.reshape(1, -1), repeats=call_prices.shape[0], axis=0) * 10

    curr_mean_syn_forward = (call_prices - put_prices + strikes).mean(axis=1) * 0.1

    next_month = mdi.opkind[op_kind]['a_yymm'][op_exp2]
    all_k = np.array(mdi.opkind[op_kind]['d_yymm_pxu_M'][next_month])
    all_k_arr = np.array([int(con[1:]) for con in all_k])
    close_k = all_k[np.argsort(np.abs(all_k_arr - etf_price.mean() * 1e3))][:3]
    close_k_arr = all_k_arr[np.argsort(np.abs(all_k_arr - etf_price.mean() * 1e3))][:3]

    call_arr = np.array([mdi.oplstt.xs((op_kind, next_month, "M", k)).C for k in close_k])
    put_arr = np.array([mdi.oplstt.xs((op_kind, next_month, "M", k)).P for k in close_k])

    call_prices = np.concatenate([pickle_read_md1m_o(con).loc[str(date)].avg.values.reshape(-1, 1) for con in call_arr],
                                 axis=1)
    put_prices = np.concatenate([pickle_read_md1m_o(con).loc[str(date)].avg.values.reshape(-1, 1) for con in put_arr],
                                axis=1)

    strikes = np.repeat(close_k_arr.reshape(1, -1), repeats=call_prices.shape[0], axis=0) * 10

    next_mean_syn_forward = (call_prices - put_prices + strikes).mean(axis=1) * 0.1

    opop_basis = pd.Series(next_mean_syn_forward - curr_mean_syn_forward, index=timestamps)

    b = pd.Series(curr_mean_syn_forward, index=timestamps)

    return opop_basis, b


def get_daily_a55(op_kind1, op_kind2, op_exp1, op_exp2, multiplier1, multiplier2, date):
    mdi = pickle_read_mdi(str(date))
    etf_price1 = pickle_read_md1m_s(op_kind1).loc[date:datetime.datetime.combine(date, datetime.time(15)), 'avg']
    etf_price2 = pickle_read_md1m_s(op_kind2).loc[date:datetime.datetime.combine(date, datetime.time(15)), 'avg']
    timestamps = etf_price1.index

    curr_month = mdi.opkind[op_kind1]['a_yymm'][op_exp1]
    all_k = np.array(mdi.opkind[op_kind1]['d_yymm_pxu_M'][curr_month])
    all_k_arr = np.array([int(con[1:]) for con in all_k])
    close_k = all_k[np.argsort(np.abs(all_k_arr - etf_price1.mean() * 1e3))][:3]
    close_k_arr = all_k_arr[np.argsort(np.abs(all_k_arr - etf_price1.mean() * 1e3))][:3]

    call_arr = np.array([mdi.oplstt.xs((op_kind1, curr_month, "M", k)).C for k in close_k])
    put_arr = np.array([mdi.oplstt.xs((op_kind1, curr_month, "M", k)).P for k in close_k])

    call_prices = np.concatenate([pickle_read_md1m_o(con).loc[str(date)].avg.values.reshape(-1, 1) for con in call_arr],
                                 axis=1)
    put_prices = np.concatenate([pickle_read_md1m_o(con).loc[str(date)].avg.values.reshape(-1, 1) for con in put_arr],
                                axis=1)

    strikes = np.repeat(close_k_arr.reshape(1, -1), repeats=call_prices.shape[0], axis=0) * 10

    mean_syn_forward1 = (call_prices - put_prices + strikes).mean(axis=1) * 0.1

    curr_month = mdi.opkind[op_kind2]['a_yymm'][op_exp1]
    all_k = np.array(mdi.opkind[op_kind2]['d_yymm_pxu_M'][curr_month])
    all_k_arr = np.array([int(con[1:]) for con in all_k])
    close_k = all_k[np.argsort(np.abs(all_k_arr - etf_price2.mean() * 1e3))][:3]
    close_k_arr = all_k_arr[np.argsort(np.abs(all_k_arr - etf_price2.mean() * 1e3))][:3]

    call_arr = np.array([mdi.oplstt.xs((op_kind2, curr_month, "M", k)).C for k in close_k])
    put_arr = np.array([mdi.oplstt.xs((op_kind2, curr_month, "M", k)).P for k in close_k])

    call_prices = np.concatenate([pickle_read_md1m_o(con).loc[str(date)].avg.values.reshape(-1, 1) for con in call_arr],
                                 axis=1)
    put_prices = np.concatenate([pickle_read_md1m_o(con).loc[str(date)].avg.values.reshape(-1, 1) for con in put_arr],
                                axis=1)

    strikes = np.repeat(close_k_arr.reshape(1, -1), repeats=call_prices.shape[0], axis=0) * 10

    mean_syn_forward2 = (call_prices - put_prices + strikes).mean(axis=1) * 0.1

    a55 = pd.Series(mean_syn_forward1 * multiplier1 - mean_syn_forward2 * multiplier2, index=timestamps)

    b = pd.Series(mean_syn_forward1, index=timestamps)

    return a55, b


def get_daily_a33(etf_kind1, etf_kind2, date, etf_multiplier=1):
    mdi = pickle_read_mdi(str(date))
    etf_price1 = pickle_read_md1m_s(etf_kind1).loc[date:datetime.datetime.combine(date, datetime.time(15)), 'avg'] * 1e3
    etf_price2 = pickle_read_md1m_s(etf_kind2).loc[date:datetime.datetime.combine(date, datetime.time(15)), 'avg'] * 1e3
    time_stamps = etf_price1.index

    a33 = etf_price1 - etf_price2 * etf_multiplier
    return a33, etf_price1


def get_daily_a51_exp(op_kind, f_kind, op_exp, f_exp, date, zero_multiplier=1):
    mdi = pickle_read_mdi(str(date))
    etf_price = pickle_read_md1m_s(op_kind).loc[date:datetime.datetime.combine(date, datetime.time(15)), 'avg']
    time_stamps = etf_price.index

    curr_month = mdi.opkind[op_kind]['a_yymm'][op_exp]
    all_k = np.array(mdi.opkind[op_kind]['d_yymm_pxu_M'][curr_month])
    all_k_arr = np.array([int(con[1:]) for con in all_k])
    close_k = all_k[np.argsort(np.abs(all_k_arr - etf_price.mean() * 1e3))][:3]
    close_k_arr = all_k_arr[np.argsort(np.abs(all_k_arr - etf_price.mean() * 1e3))][:3]

    call_arr = np.array([mdi.oplstt.xs((op_kind, curr_month, "M", k)).C for k in close_k])
    put_arr = np.array([mdi.oplstt.xs((op_kind, curr_month, "M", k)).P for k in close_k])

    call_prices = np.concatenate([pickle_read_md1m_o(con).loc[str(date)].avg.values.reshape(-1, 1) for con in call_arr],
                                 axis=1)
    put_prices = np.concatenate([pickle_read_md1m_o(con).loc[str(date)].avg.values.reshape(-1, 1) for con in put_arr],
                                axis=1)

    strikes = np.repeat(close_k_arr.reshape(1, -1), repeats=call_prices.shape[0], axis=0) * 10

    mean_syn_forward = (call_prices - put_prices + strikes).mean(axis=1) * 0.1

    curr_month_f = mdi.fkind[f_kind]['a_icode'][f_exp]

    future_price = pickle_read_md1m_f(curr_month_f).loc[str(date)].avg.values

    a51 = pd.Series(mean_syn_forward * zero_multiplier - future_price, index=time_stamps)

    b = pd.Series(mean_syn_forward, index=time_stamps)

    adj_mean_syn_forward = []
    for t in range(mean_syn_forward.shape[0]):
        if t < 120:
            adj_mean_syn_forward.append(mean_syn_forward[t])
        else:
            adj_avg = mean_syn_forward[120:t].mean() if t > 120 else 0.
            adj_w = (t - 120) / 120
            adj_val = adj_avg * adj_w + mean_syn_forward[t] * (1 - adj_w)
            adj_mean_syn_forward.append(adj_val)

    adj_mean_syn_forward = np.array(adj_mean_syn_forward)
    a51 = pd.Series(adj_mean_syn_forward * zero_multiplier - future_price, index=time_stamps)

    return a51, b


def get_daily_a13_exp(etf_kind, f_kind, f_exp, date, zero_multiplier=1):
    mdi = pickle_read_mdi(str(date))
    etf_price = pickle_read_md1m_s(etf_kind).loc[date:datetime.datetime.combine(date, datetime.time(15)), 'avg'] * 1e3
    time_stamps = etf_price.index

    curr_month_f = mdi.fkind[f_kind]['a_icode'][f_exp]

    future_price = pickle_read_md1m_f(curr_month_f).loc[str(date)].avg.values

    a13 = pd.Series(future_price - etf_price * zero_multiplier, index=time_stamps)

    b = pd.Series(etf_price, index=time_stamps)

    adj_etf_price = []
    for t in range(etf_price.shape[0]):
        if t < 120:
            adj_etf_price.append(etf_price.values[t])
        else:
            adj_avg = etf_price.values[120:t].mean() if t > 120 else 0.
            adj_w = (t - 120) / 120
            adj_val = adj_avg * adj_w + etf_price.values[t] * (1 - adj_w)
            adj_etf_price.append(adj_val)

    adj_etf_price = np.array(adj_etf_price)

    a13 = pd.Series(future_price - adj_etf_price * zero_multiplier, index=time_stamps)

    return a13, b


def get_daily_a61(date, f_kind, exp=0):
    date = str(date)

    con_lst = jq.get_price(security=jq.get_future_contracts(f_kind, date), start_date=date + ' 09:00:00',
                           end_date=date + ' 15:30:00', \
                           fields=['open_interest']).sort_values(by=['open_interest'], ascending=False).code.values

    info = pickle_read_seclst(str(date))
    info = info[(info.type == 'options') & (info.name.str.startswith(con_lst[exp].split('.')[0]))]
    if info.shape[0] == 0:
        exp += 1
        info = pickle_read_seclst(str(date))
        info = info[(info.type == 'options') & (info.name.str.startswith(con_lst[exp].split('.')[0]))]

    future_price = jq.get_price(security=con_lst[exp], start_date=date + ' 09:00:00', end_date=date + ' 15:30:00',
                                fields=['avg'], frequency='1m')

    info['strike'] = [int(name.split('月')[-1]) for name in info.display_name]
    info['type'] = ['C' if '购' in name else 'P' for name in info.display_name]

    call_info = info[info.type == 'C']
    put_info = info[info.type == 'P']

    calls = list(call_info.strike[np.argsort((call_info.strike - future_price.avg.iloc[0]).abs())].iloc[:3].index)
    puts = list(put_info.strike[np.argsort((put_info.strike - future_price.avg.iloc[0]).abs())].iloc[:3].index)

    op_price = jq.get_price(security=list(calls + puts), start_date=date + ' 09:00:00', end_date=date + ' 15:30:00', \
                            fields=['avg'], frequency='1m')
    op_price = op_price.pivot(index=['time'], columns=['code']).avg[list(calls + puts)]

    syn_forward1 = op_price.iloc[:, 0] - op_price.iloc[:, 3] + info[info.name == op_price.columns[0]].strike.iloc[0]
    syn_forward2 = op_price.iloc[:, 1] - op_price.iloc[:, 4] + info[info.name == op_price.columns[1]].strike.iloc[0]
    syn_forward3 = op_price.iloc[:, 2] - op_price.iloc[:, 5] + info[info.name == op_price.columns[2]].strike.iloc[0]

    a61_1 = syn_forward1 - future_price.avg
    a61_2 = syn_forward2 - future_price.avg
    a61_3 = syn_forward3 - future_price.avg
    a61 = (a61_1 + a61_2 + a61_3) / 3

    return a61, future_price


def get_history_a5(op_kind, etf_kind, op_exp, starting_date, ending_date, op_multiplier=1, etf_multiplier=None):
    starting_date = datetime.datetime.strptime(str(starting_date), '%Y-%m-%d').date()
    ending_date = datetime.datetime.strptime(str(ending_date), '%Y-%m-%d').date()

    auto_zero = True if not etf_multiplier else False

    a5 = {'abs': {}, 'b': {}}

    all_t_days = get_all_tradedays(starting_date, ending_date)

    m_record = 1e8

    for date in tqdm(all_t_days[:]):

        curr_month = get_month(True, False, op_kind, None, date)

        if auto_zero:
            asset_price1 = pickle_read_md1m_s(op_kind).loc[str(date)].close.mean()
            asset_price2 = pickle_read_md1m_s(etf_kind).loc[str(date)].close.mean()
            multiplier_ = round(asset_price1 / asset_price2, 3)

            if abs(multiplier_ - m_record) > 0.01:
                etf_multiplier = multiplier_

            m_record = etf_multiplier

        if curr_month not in a5['abs']:
            a5['abs'][curr_month] = []
        if curr_month not in a5['b']:
            a5['b'][curr_month] = []

        daily_a5, b = get_daily_a5(op_kind, etf_kind, op_exp, date, op_multiplier, etf_multiplier)

        a5['abs'][curr_month].append(daily_a5)
        a5['b'][curr_month].append(b)

    for month in a5['abs'].keys():
        a5['abs'][month] = pd.concat(a5['abs'][month])

    for month in a5['b'].keys():
        a5['b'][month] = pd.concat(a5['b'][month])

    return a5


# def get_history_a5_xie(op_kind, etf_kind, op_exp, starting_date, ending_date, op_multiplier=1, etf_multiplier=1):

#     a5 = {'abs':{}, 'b':{}}

#     all_t_days = get_all_tradedays(starting_date, ending_date)

#     for date in tqdm(all_t_days[:]):

#         # print(date)

#         curr_month = get_month(True, False, op_kind, None, date)

#         if curr_month not in a5['abs']:
#             a5['abs'][curr_month] = []
#         if curr_month not in a5['b']:
#             a5['b'][curr_month] = []

#         daily_a5, b = get_daily_a5_xie(op_kind, etf_kind, op_exp, date, op_multiplier, etf_multiplier)

#         a5['abs'][curr_month].append(daily_a5)
#         a5['b'][curr_month].append(b)

#     for month in a5['abs'].keys():
#         a5['abs'][month] = pd.concat(a5['abs'][month])

#     for month in a5['b'].keys():
#         a5['b'][month] = pd.concat(a5['b'][month])

#     return a5




def get_history_a13(etf_kind, f_kind, f_exp, starting_date, ending_date, zero_multiplier=None, whether_adjust=True):

    starting_date = datetime.datetime.strptime(str(starting_date), '%Y-%m-%d').date()
    ending_date = datetime.datetime.strptime(str(ending_date), '%Y-%m-%d').date()

    a13 = {'abs': {}, 'rel': {}, 'b': {}}

    all_t_days = get_all_tradedays(starting_date, ending_date)

    today_month = get_month(False, True, etf_kind, f_kind, all_t_days[-1])

    all_fu_ex_dates = get_all_fu_ex_dates()

    whether_inbetween = False

    if not zero_multiplier:
        auto_compute_zero = True
    else:
        auto_compute_zero = False

    zero_exist = False

    all_etf_dividend_dates = np.sort(all_dividend_info[all_dividend_info.code == etf_kind].ex_date.values)

    for date in tqdm(all_t_days[:]):

        curr_month = get_month(False, True, etf_kind, f_kind, date)

        if whether_adjust:
            if auto_compute_zero:
                if not zero_exist or date in all_etf_dividend_dates:
                    op_kind = etf_kind
                    op_kind_dict = {
                        '510050': ('IH', '000016', '50ETF', 30, 1),
                        '510300': ('IF', '000300', '300ETF', 30, 1),
                        '159919': ('IF', '000300', '919ETF', 30, 1),
                        '510500': ('IC', '000905', '500ETF', 20, 1),
                        '512100': ('IM', '000852', '1000ETF', 50, 2.5),
                    }
                    denominator = op_kind_dict[op_kind][3]
                    etf_price = pickle_read_md1d_s(op_kind).loc[str(date)].pre_close * 1000
                    index_price = pickle_read_md1d_i(op_kind_dict[op_kind][1]).loc[str(date)].pre_close
                    zero_multiplier_ext = (index_price / (etf_price * op_kind_dict[op_kind][4])) * denominator
                    zero_multiplier_dec = zero_multiplier_ext - int(zero_multiplier_ext)
                    dec_choices = np.array([0., 0.2, 0.25, 0.33, 0.5, 0.67, 0.75, 0.8, 1.])
                    dec_choice = dec_choices[np.argmin(np.abs(zero_multiplier_dec - dec_choices))]
                    zero_multiplier = (int((int(zero_multiplier_ext) + dec_choice) * 100 + 1e-8) / 100) * \
                                      op_kind_dict[op_kind][4] / denominator
                    zero_exist = True 
        else:
            zero_multiplier = 1

        if date in all_fu_ex_dates:
            zero_exist = False

        if curr_month == today_month:
            zero_multiplier = curr_multiplier[f_kind]

        daily_zero_normal = get_zero(f_kind, f_exp, date, zero_multiplier)

        if curr_month not in a13['abs']:
            a13['abs'][curr_month] = []
        if curr_month not in a13['rel']:
            a13['rel'][curr_month] = []
        if curr_month not in a13['b']:
            a13['b'][curr_month] = []

        next_fu_ex_date = all_fu_ex_dates[all_fu_ex_dates >= date][0]

        if date not in all_fu_ex_dates:
            daily_a13, b = get_daily_a13(etf_kind, f_kind, f_exp, date, zero_multiplier)
        else:
            daily_a13, b = get_daily_a13_exp(etf_kind, f_kind, f_exp, date, zero_multiplier)

        a13['abs'][curr_month].append(daily_a13)

        a13['rel'][curr_month].append(daily_a13 + daily_zero_normal)

        a13['b'][curr_month].append(b)

    for month in a13['abs'].keys():
        a13['abs'][month] = pd.concat(a13['abs'][month])

    for month in a13['rel'].keys():
        a13['rel'][month] = pd.concat(a13['rel'][month])

    for month in a13['b'].keys():
        a13['b'][month] = pd.concat(a13['b'][month])

    return a13





def get_history_a51(op_kind, f_kind, starting_date, ending_date, zero_multiplier=None, whether_adjust=True):

    starting_date = datetime.datetime.strptime(str(starting_date), '%Y-%m-%d').date()
    ending_date = datetime.datetime.strptime(str(ending_date), '%Y-%m-%d').date()

    a51 = {'abs': {}, 'rel': {}, 'b': {}}

    all_t_days = get_all_tradedays(starting_date, ending_date)

    today_month = get_month(False, True, op_kind, f_kind, all_t_days[-1])

    all_op_ex_dates = get_all_op_ex_dates()
    all_fu_ex_dates = get_all_fu_ex_dates()

    whether_inbetween = False

    if not zero_multiplier:
        auto_compute_zero = True
    else:
        auto_compute_zero = False

    zero_exist = False

    all_etf_dividend_dates = np.sort(all_dividend_info[all_dividend_info.code == op_kind].ex_date.values)

    for date in tqdm(all_t_days[:]):

        curr_month = get_month(False, True, op_kind, f_kind, date)

        if whether_adjust:
            if auto_compute_zero:
                if not zero_exist or date in all_etf_dividend_dates:
                    op_kind_dict = {
                        '510050': ('IH', '000016', '50ETF', 30, 1),
                        '510300': ('IF', '000300', '300ETF', 30, 1),
                        '159919': ('IF', '000300', '919ETF', 30, 1),
                        '510500': ('IC', '000905', '500ETF', 20, 1),
                        '512100': ('IM', '000852', '1000ETF', 50, 2.5),
                    }
                    denominator = op_kind_dict[op_kind][3]
                    etf_price = pickle_read_md1d_s(op_kind).loc[str(date)].pre_close * 1000
                    index_price = pickle_read_md1d_i(op_kind_dict[op_kind][1]).loc[str(date)].pre_close
                    zero_multiplier_ext = (index_price / (etf_price * op_kind_dict[op_kind][4])) * denominator
                    zero_multiplier_dec = zero_multiplier_ext - int(zero_multiplier_ext)
                    dec_choices = np.array([0., 0.2, 0.25, 0.33, 0.5, 0.67, 0.75, 0.8, 1.])
                    dec_choice = dec_choices[np.argmin(np.abs(zero_multiplier_dec - dec_choices))]
                    zero_multiplier = (int((int(zero_multiplier_ext) + dec_choice) * 100 + 1e-8) / 100) * \
                                       op_kind_dict[op_kind][4] / denominator
                    zero_exist = True 
        else:
            zero_multiplier = 1

        if date in all_fu_ex_dates:
            zero_exist = False

        if curr_month == today_month:
            zero_multiplier = curr_multiplier[f_kind]

        daily_zero_normal = get_zero(f_kind, 0, date, zero_multiplier)

        daily_zero_inbetween = get_zero(f_kind, 0, date, zero_multiplier)

        if curr_month not in a51['abs']:
            a51['abs'][curr_month] = []
        if curr_month not in a51['rel']:
            a51['rel'][curr_month] = []
        if curr_month not in a51['b']:
            a51['b'][curr_month] = []

        next_op_ex_date = all_op_ex_dates[all_op_ex_dates >= date][0]
        next_fu_ex_date = all_fu_ex_dates[all_fu_ex_dates >= date][0]

        whether_inbetween = False if next_fu_ex_date < next_op_ex_date else True

        if not whether_inbetween:

            if date not in all_fu_ex_dates:
                daily_a51, b = get_daily_a51(op_kind, f_kind, 0, 0, date, zero_multiplier)
            else:
                daily_a51, b = get_daily_a51_exp(op_kind, f_kind, 0, 0, date, zero_multiplier)

            a51['abs'][curr_month].append(daily_a51)

            a51['rel'][curr_month].append(daily_a51 - daily_zero_normal)

            a51['b'][curr_month].append(b)

        else:

            if date not in all_fu_ex_dates:
                daily_a51, b = get_daily_a51(op_kind, f_kind, 1, 0, date, zero_multiplier)
            else:
                daily_a51, b = get_daily_a51_exp(op_kind, f_kind, 1, 0, date, zero_multiplier)

            a51['abs'][curr_month].append(daily_a51)

            a51['rel'][curr_month].append(daily_a51 - daily_zero_inbetween)

            a51['b'][curr_month].append(b)

    for month in a51['abs'].keys():
        a51['abs'][month] = pd.concat(a51['abs'][month])

    for month in a51['rel'].keys():
        a51['rel'][month] = pd.concat(a51['rel'][month])

    for month in a51['b'].keys():
        a51['b'][month] = pd.concat(a51['b'][month])

    return a51




def get_history_ca51(op_kind, f_kind, starting_date, ending_date, zero_multiplier=None, whether_adjust=True):

    starting_date = datetime.datetime.strptime(str(starting_date), '%Y-%m-%d').date()
    ending_date = datetime.datetime.strptime(str(ending_date), '%Y-%m-%d').date()

    a51 = {'abs': {}, 'rel': {}, 'b': {}}

    all_t_days = get_all_tradedays(starting_date, ending_date)

    today_month = get_month(False, True, op_kind, f_kind, all_t_days[-1])

    all_op_ex_dates = get_all_op_ex_dates()
    all_fu_ex_dates = get_all_fu_ex_dates()

    whether_inbetween = False

    if not zero_multiplier:
        auto_compute_zero = True
    else:
        auto_compute_zero = False

    zero_exist = False

    all_etf_dividend_dates = np.sort(all_dividend_info[all_dividend_info.code == op_kind].ex_date.values)

    for date in tqdm(all_t_days[:]):

        curr_month = get_month(False, False, op_kind, f_kind, date)

        next_op_ex_date = all_op_ex_dates[all_op_ex_dates >= date][0]
        next_fu_ex_date = all_fu_ex_dates[all_fu_ex_dates >= date][0]

        whether_inbetween = False if next_fu_ex_date < next_op_ex_date else True

        if whether_adjust:
            if auto_compute_zero:
                if not zero_exist or date in all_etf_dividend_dates:
                    op_kind_dict = {
                        '510050': ('IH', '000016', '50ETF', 30, 1),
                        '510300': ('IF', '000300', '300ETF', 30, 1),
                        '159919': ('IF', '000300', '919ETF', 30, 1),
                        '510500': ('IC', '000905', '500ETF', 20, 1),
                        '512100': ('IM', '000852', '1000ETF', 50, 2.5),
                    }
                    denominator = op_kind_dict[op_kind][3]
                    etf_price = pickle_read_md1d_s(op_kind).loc[str(date)].pre_close * 1000
                    index_price = pickle_read_md1d_i(op_kind_dict[op_kind][1]).loc[str(date)].pre_close
                    zero_multiplier_ext = (index_price / (etf_price * op_kind_dict[op_kind][4])) * denominator
                    zero_multiplier_dec = zero_multiplier_ext - int(zero_multiplier_ext)
                    dec_choices = np.array([0., 0.2, 0.25, 0.33, 0.5, 0.67, 0.75, 0.8, 1.])
                    dec_choice = dec_choices[np.argmin(np.abs(zero_multiplier_dec - dec_choices))]
                    zero_multiplier = (int((int(zero_multiplier_ext) + dec_choice) * 100 + 1e-8) / 100) * \
                                       op_kind_dict[op_kind][4] / denominator
                    zero_exist = True 
        else:
            zero_multiplier = 1

        if curr_month == today_month:
            zero_multiplier = curr_multiplier[f_kind]

        if date in all_op_ex_dates:
            zero_exist = False

        daily_zero_normal = get_zero(f_kind, 1, date, zero_multiplier)

        daily_zero_inbetween = get_zero(f_kind, 0, date, zero_multiplier)

        if curr_month not in a51['abs']:    
            a51['abs'][curr_month] = []
        if curr_month not in a51['rel']:
            a51['rel'][curr_month] = []
        if curr_month not in a51['b']:
            a51['b'][curr_month] = []

        if not whether_inbetween:

            daily_a51, b = get_daily_a51(op_kind, f_kind, 0, 1, date, zero_multiplier)

            a51['abs'][curr_month].append(daily_a51)

            a51['rel'][curr_month].append(daily_a51 - daily_zero_normal)

            a51['b'][curr_month].append(b)

        else:

            daily_a51, b = get_daily_a51(op_kind, f_kind, 0, 0, date, zero_multiplier)

            a51['abs'][curr_month].append(daily_a51)

            a51['rel'][curr_month].append(daily_a51 - daily_zero_inbetween)

            a51['b'][curr_month].append(b)

    for month in a51['abs'].keys():
        a51['abs'][month] = pd.concat(a51['abs'][month])

    for month in a51['rel'].keys():
        a51['rel'][month] = pd.concat(a51['rel'][month])

    for month in a51['b'].keys():
        a51['b'][month] = pd.concat(a51['b'][month])

    return a51



def get_history_nca51(op_kind, f_kind, starting_date, ending_date, zero_multiplier=None, whether_adjust=True):
    starting_date = datetime.datetime.strptime(str(starting_date), '%Y-%m-%d').date()
    ending_date = datetime.datetime.strptime(str(ending_date), '%Y-%m-%d').date()

    a51 = {'abs': {}, 'rel': {}, 'b': {}}

    all_t_days = get_all_tradedays(starting_date, ending_date)

    today_month = get_month(False, True, op_kind, f_kind, all_t_days[-1])

    all_op_ex_dates = get_all_op_ex_dates()
    all_fu_ex_dates = get_all_fu_ex_dates()

    whether_inbetween = False

    if not zero_multiplier:
        auto_compute_zero = True
    else:
        auto_compute_zero = False

    zero_exist = False

    all_etf_dividend_dates = np.sort(all_dividend_info[all_dividend_info.code == op_kind].ex_date.values)

    for date in tqdm(all_t_days[:]):

        curr_month_f = get_month(False, True, op_kind, f_kind, date)

        curr_month = get_month(False, False, op_kind, f_kind, date)

        next_op_ex_date = all_op_ex_dates[all_op_ex_dates >= date][0]
        next_fu_ex_date = all_fu_ex_dates[all_fu_ex_dates >= date][0]

        whether_inbetween = False if next_fu_ex_date < next_op_ex_date else True

        if whether_adjust:
            if auto_compute_zero:
                if not zero_exist or date in all_etf_dividend_dates:
                    op_kind_dict = {
                        '510050': ('IH', '000016', '50ETF', 30, 1),
                        '510300': ('IF', '000300', '300ETF', 30, 1),
                        '159919': ('IF', '000300', '919ETF', 30, 1),
                        '510500': ('IC', '000905', '500ETF', 20, 1),
                        '512100': ('IM', '000852', '1000ETF', 50, 2.5),
                    }
                    denominator = op_kind_dict[op_kind][3]
                    etf_price = pickle_read_md1d_s(op_kind).loc[str(date)].pre_close * 1000
                    index_price = pickle_read_md1d_i(op_kind_dict[op_kind][1]).loc[str(date)].pre_close
                    zero_multiplier_ext = (index_price / (etf_price * op_kind_dict[op_kind][4])) * denominator
                    zero_multiplier_dec = zero_multiplier_ext - int(zero_multiplier_ext)
                    dec_choices = np.array([0., 0.2, 0.25, 0.33, 0.5, 0.67, 0.75, 0.8, 1.])
                    dec_choice = dec_choices[np.argmin(np.abs(zero_multiplier_dec - dec_choices))]
                    zero_multiplier = (int((int(zero_multiplier_ext) + dec_choice) * 100 + 1e-8) / 100) * \
                                       op_kind_dict[op_kind][4] / denominator
                    zero_exist = True 
        else:
            zero_multiplier = 1

        if date in all_op_ex_dates:
            zero_exist = False

        if curr_month == today_month:
            zero_multiplier = curr_multiplier[f_kind]

        daily_zero_normal = get_zero(f_kind, 2, date, zero_multiplier)

        if curr_month_f[-2:] in ['02', '05', '08', '11']:
            daily_zero_inbetween = get_zero(f_kind, 1, date, zero_multiplier)
        else:
            daily_zero_inbetween = get_zero(f_kind, 2, date, zero_multiplier)

        if curr_month not in a51['abs']:
            a51['abs'][curr_month] = []
        if curr_month not in a51['rel']:
            a51['rel'][curr_month] = []
        if curr_month not in a51['b']:
            a51['b'][curr_month] = []

        if not whether_inbetween:

            daily_a51, b = get_daily_a51(op_kind, f_kind, 0, 2, date, zero_multiplier)

            a51['abs'][curr_month].append(daily_a51)

            a51['rel'][curr_month].append(daily_a51 - daily_zero_normal)

            a51['b'][curr_month].append(b)

        else:

            if curr_month_f[-2:] in ['02', '05', '08', '11']:
                daily_a51, b = get_daily_a51(op_kind, f_kind, 0, 1, date, zero_multiplier)
            else:
                daily_a51, b = get_daily_a51(op_kind, f_kind, 0, 2, date, zero_multiplier)

            a51['abs'][curr_month].append(daily_a51)

            a51['rel'][curr_month].append(daily_a51 - daily_zero_inbetween)

            a51['b'][curr_month].append(b)

    for month in a51['abs'].keys():
        a51['abs'][month] = pd.concat(a51['abs'][month])    

    for month in a51['rel'].keys():
        a51['rel'][month] = pd.concat(a51['rel'][month])

    for month in a51['b'].keys():
        a51['b'][month] = pd.concat(a51['b'][month])

    return a51


def get_history_ka51(op_kind, f_kind, starting_date, ending_date, zero_multiplier=None, whether_adjust=True):
    starting_date = datetime.datetime.strptime(str(starting_date), '%Y-%m-%d').date()
    ending_date = datetime.datetime.strptime(str(ending_date), '%Y-%m-%d').date()

    a51 = {'abs': {}, 'rel': {}, 'b': {}}

    all_t_days = get_all_tradedays(starting_date, ending_date)

    today_month = get_month(False, True, op_kind, f_kind, all_t_days[-1])

    all_op_ex_dates = get_all_op_ex_dates()
    all_fu_ex_dates = get_all_fu_ex_dates()

    whether_inbetween = False

    if not zero_multiplier:
        auto_compute_zero = True
    else:
        auto_compute_zero = False

    zero_exist = False

    all_etf_dividend_dates = np.sort(all_dividend_info[all_dividend_info.code == op_kind].ex_date.values)

    for date in tqdm(all_t_days[:]):

        curr_month = get_month(False, True, op_kind, f_kind, date)

        if whether_adjust:
            if auto_compute_zero:
                if not zero_exist or date in all_etf_dividend_dates:
                    op_kind_dict = {
                        '510050': ('IH', '000016', '50ETF', 30, 1),
                        '510300': ('IF', '000300', '300ETF', 30, 1),
                        '159919': ('IF', '000300', '919ETF', 30, 1),
                        '510500': ('IC', '000905', '500ETF', 20, 1),
                        '512100': ('IM', '000852', '1000ETF', 50, 2.5),
                    }
                    denominator = op_kind_dict[op_kind][3]
                    etf_price = pickle_read_md1d_s(op_kind).loc[str(date)].pre_close * 1000
                    index_price = pickle_read_md1d_i(op_kind_dict[op_kind][1]).loc[str(date)].pre_close
                    zero_multiplier_ext = (index_price / (etf_price * op_kind_dict[op_kind][4])) * denominator
                    zero_multiplier_dec = zero_multiplier_ext - int(zero_multiplier_ext)
                    dec_choices = np.array([0., 0.2, 0.25, 0.33, 0.5, 0.67, 0.75, 0.8, 1.])
                    dec_choice = dec_choices[np.argmin(np.abs(zero_multiplier_dec - dec_choices))]
                    zero_multiplier = (int((int(zero_multiplier_ext) + dec_choice) * 100 + 1e-8) / 100) * \
                                       op_kind_dict[op_kind][4] / denominator
                    zero_exist = True 
        else:
            zero_multiplier = 1

        if curr_month == today_month:
            zero_multiplier = curr_multiplier[f_kind]

        if date in all_fu_ex_dates:
            zero_exist = False

        daily_zero_normal = get_zero(f_kind, 0, date, zero_multiplier)

        if curr_month not in a51['abs']:
            a51['abs'][curr_month] = []
        if curr_month not in a51['rel']:
            a51['rel'][curr_month] = []
        if curr_month not in a51['b']:
            a51['b'][curr_month] = []

        next_op_ex_date = all_op_ex_dates[all_op_ex_dates >= date][0]
        next_fu_ex_date = all_fu_ex_dates[all_fu_ex_dates >= date][0]

        whether_inbetween = False if next_fu_ex_date < next_op_ex_date else True

        if not whether_inbetween:
            daily_a51, b = get_daily_a51(op_kind, f_kind, 1, 0, date, zero_multiplier)

            a51['abs'][curr_month].append(daily_a51)

            a51['rel'][curr_month].append(daily_a51 - daily_zero_normal)

            a51['b'][curr_month].append(b)

    for month in a51['abs'].keys():
        if len(a51['abs'][month]) > 0:
            a51['abs'][month] = pd.concat(a51['abs'][month])
        else:
            a51['abs'][month] = pd.Series()

    for month in a51['rel'].keys():
        if len(a51['rel'][month]) > 0:
            a51['rel'][month] = pd.concat(a51['rel'][month])
        else:
            a51['rel'][month] = pd.Series()

    for month in a51['b'].keys():
        if len(a51['b'][month]) > 0:
            a51['b'][month] = pd.concat(a51['b'][month])
        else:
            a51['b'][month] = pd.Series()

    return a51




def get_history_na51(op_kind, f_kind, starting_date, ending_date, zero_multiplier=None, whether_adjust=True):
    starting_date = datetime.datetime.strptime(str(starting_date), '%Y-%m-%d').date()
    ending_date = datetime.datetime.strptime(str(ending_date), '%Y-%m-%d').date()

    a51 = {'abs': {}, 'rel': {}, 'b': {}}

    all_t_days = get_all_tradedays(starting_date, ending_date)

    today_month = get_month(False, True, op_kind, f_kind, all_t_days[-1])

    all_op_ex_dates = get_all_op_ex_dates()
    all_fu_ex_dates = get_all_fu_ex_dates()

    whether_inbetween = False

    if not zero_multiplier:
        auto_compute_zero = True
    else:
        auto_compute_zero = False

    zero_exist = False

    all_etf_dividend_dates = np.sort(all_dividend_info[all_dividend_info.code == op_kind].ex_date.values)

    for date in tqdm(all_t_days[:]):

        curr_month = get_month(False, False, op_kind, f_kind, date)

        next_op_ex_date = all_op_ex_dates[all_op_ex_dates >= date][0]
        next_fu_ex_date = all_fu_ex_dates[all_fu_ex_dates >= date][0]

        whether_inbetween = False if next_fu_ex_date < next_op_ex_date else True

        if whether_adjust:
            if auto_compute_zero:
                if not zero_exist or date in all_etf_dividend_dates:
                    op_kind_dict = {
                        '510050': ('IH', '000016', '50ETF', 30, 1),
                        '510300': ('IF', '000300', '300ETF', 30, 1),
                        '159919': ('IF', '000300', '919ETF', 30, 1),
                        '510500': ('IC', '000905', '500ETF', 20, 1),
                        '512100': ('IM', '000852', '1000ETF', 50, 2.5),
                    }
                    denominator = op_kind_dict[op_kind][3]
                    etf_price = pickle_read_md1d_s(op_kind).loc[str(date)].pre_close * 1000
                    index_price = pickle_read_md1d_i(op_kind_dict[op_kind][1]).loc[str(date)].pre_close
                    zero_multiplier_ext = (index_price / (etf_price * op_kind_dict[op_kind][4])) * denominator
                    zero_multiplier_dec = zero_multiplier_ext - int(zero_multiplier_ext)
                    dec_choices = np.array([0., 0.2, 0.25, 0.33, 0.5, 0.67, 0.75, 0.8, 1.])
                    dec_choice = dec_choices[np.argmin(np.abs(zero_multiplier_dec - dec_choices))]
                    zero_multiplier = (int((int(zero_multiplier_ext) + dec_choice) * 100 + 1e-8) / 100) * \
                                       op_kind_dict[op_kind][4] / denominator
                    zero_exist = True 
        else:
            zero_multiplier = 1

        if date in all_op_ex_dates:
            zero_exist = False

        if curr_month == today_month:
            zero_multiplier = curr_multiplier[f_kind]

        daily_zero_normal = get_zero(f_kind, 1, date, zero_multiplier)

        daily_zero_inbetween = get_zero(f_kind, 0, date, zero_multiplier)

        if curr_month not in a51['abs']:
            a51['abs'][curr_month] = []
        if curr_month not in a51['rel']:
            a51['rel'][curr_month] = []
        if curr_month not in a51['b']:
            a51['b'][curr_month] = []

        if not whether_inbetween:

            daily_a51, b = get_daily_a51(op_kind, f_kind, 1, 1, date, zero_multiplier)

            a51['abs'][curr_month].append(daily_a51)

            a51['rel'][curr_month].append(daily_a51 - daily_zero_normal)    

            a51['b'][curr_month].append(b)

        else:

            daily_a51, b = get_daily_a51(op_kind, f_kind, 1, 0, date, zero_multiplier)

            a51['abs'][curr_month].append(daily_a51)    

            a51['rel'][curr_month].append(daily_a51 - daily_zero_inbetween)

            a51['b'][curr_month].append(b)

    for month in a51['abs'].keys():
        a51['abs'][month] = pd.concat(a51['abs'][month])

    for month in a51['rel'].keys():
        a51['rel'][month] = pd.concat(a51['rel'][month])

    for month in a51['b'].keys():
        a51['b'][month] = pd.concat(a51['b'][month])

    return a51


def get_history_sa51(op_kind, f_kind, starting_date, ending_date, zero_multiplier=None, whether_adjust=True):
    starting_date = datetime.datetime.strptime(str(starting_date), '%Y-%m-%d').date()
    ending_date = datetime.datetime.strptime(str(ending_date), '%Y-%m-%d').date()

    a51 = {'abs': {}, 'rel': {}, 'b': {}}

    all_t_days = get_all_tradedays(starting_date, ending_date)

    today_month = get_month(False, True, op_kind, f_kind, all_t_days[-1])

    all_op_ex_dates = get_all_op_ex_dates()
    all_fu_ex_dates = get_all_fu_ex_dates()

    whether_inbetween = False

    if not zero_multiplier:
        auto_compute_zero = True
    else:
        auto_compute_zero = False

    zero_exist = False

    all_etf_dividend_dates = np.sort(all_dividend_info[all_dividend_info.code == op_kind].ex_date.values)

    for date in tqdm(all_t_days[:]):

        curr_month = get_month(False, False, op_kind, f_kind, date)
        curr_month_f = get_month(False, True, op_kind, f_kind, date)

        next_op_ex_date = all_op_ex_dates[all_op_ex_dates >= date][0]
        next_fu_ex_date = all_fu_ex_dates[all_fu_ex_dates >= date][0]

        whether_inbetween = False if next_fu_ex_date < next_op_ex_date else True

        if whether_adjust:
            if auto_compute_zero:
                if not zero_exist or date in all_etf_dividend_dates:
                    op_kind_dict = {
                        '510050': ('IH', '000016', '50ETF', 30, 1),
                        '510300': ('IF', '000300', '300ETF', 30, 1),
                        '159919': ('IF', '000300', '919ETF', 30, 1),
                        '510500': ('IC', '000905', '500ETF', 20, 1),
                        '512100': ('IM', '000852', '1000ETF', 50, 2.5),
                    }
                    denominator = op_kind_dict[op_kind][3]
                    etf_price = pickle_read_md1d_s(op_kind).loc[str(date)].pre_close * 1000
                    index_price = pickle_read_md1d_i(op_kind_dict[op_kind][1]).loc[str(date)].pre_close
                    zero_multiplier_ext = (index_price / (etf_price * op_kind_dict[op_kind][4])) * denominator
                    zero_multiplier_dec = zero_multiplier_ext - int(zero_multiplier_ext)
                    dec_choices = np.array([0., 0.2, 0.25, 0.33, 0.5, 0.67, 0.75, 0.8, 1.])
                    dec_choice = dec_choices[np.argmin(np.abs(zero_multiplier_dec - dec_choices))]
                    zero_multiplier = (int((int(zero_multiplier_ext) + dec_choice) * 100 + 1e-8) / 100) * \
                                       op_kind_dict[op_kind][4] / denominator
                    zero_exist = True 
        else:
            zero_multiplier = 1

        if date in all_fu_ex_dates:
            zero_exist = False

        if curr_month == today_month:
            zero_multiplier = curr_multiplier[f_kind]

        if curr_month_f[-2:] in ['02', '05', '08', '11']:
            daily_zero_inbetween = get_zero(f_kind, 1, date, zero_multiplier)
        else:
            daily_zero_inbetween = get_zero(f_kind, 2, date, zero_multiplier)

        daily_zero_normal = get_zero(f_kind, 2, date, zero_multiplier)

        if curr_month not in a51['abs']:
            a51['abs'][curr_month] = []
        if curr_month not in a51['rel']:
            a51['rel'][curr_month] = []
        if curr_month not in a51['b']:
            a51['b'][curr_month] = []

        if not whether_inbetween:

            daily_a51, b = get_daily_a51(op_kind, f_kind, 2, 2, date, zero_multiplier)

            a51['abs'][curr_month].append(daily_a51)

            a51['rel'][curr_month].append(daily_a51 - daily_zero_normal)

            a51['b'][curr_month].append(b)

        else:

            if curr_month_f[-2:] in ['02', '05', '08', '11']:
                daily_a51, b = get_daily_a51(op_kind, f_kind, 2, 1, date, zero_multiplier)
            else:
                daily_a51, b = get_daily_a51(op_kind, f_kind, 2, 2, date, zero_multiplier)

            a51['abs'][curr_month].append(daily_a51)

            a51['rel'][curr_month].append(daily_a51 - daily_zero_inbetween)

            a51['b'][curr_month].append(b)

    for month in a51['abs'].keys():
        a51['abs'][month] = pd.concat(a51['abs'][month])

    for month in a51['rel'].keys():
        a51['rel'][month] = pd.concat(a51['rel'][month])

    for month in a51['b'].keys():
        a51['b'][month] = pd.concat(a51['b'][month])

    return a51



def get_history_nsa51(op_kind, f_kind, starting_date, ending_date, zero_multiplier=None, whether_adjust=True):
    starting_date = datetime.datetime.strptime(str(starting_date), '%Y-%m-%d').date()
    ending_date = datetime.datetime.strptime(str(ending_date), '%Y-%m-%d').date()

    a51 = {'abs': {}, 'b': {}}

    all_t_days = get_all_tradedays(starting_date, ending_date)

    today_month = get_month(False, True, op_kind, f_kind, all_t_days[-1])

    all_op_ex_dates = get_all_op_ex_dates()
    all_fu_ex_dates = get_all_fu_ex_dates()

    whether_inbetween = False

    if not zero_multiplier:
        auto_compute_zero = True
    else:
        auto_compute_zero = False

    zero_exist = False

    all_etf_dividend_dates = np.sort(all_dividend_info[all_dividend_info.code == op_kind].ex_date.values)

    for date in tqdm(all_t_days[:]):

        curr_month = get_month(False, False, op_kind, f_kind, date)

        curr_month_f = get_month(False, True, op_kind, f_kind, date)

        next_op_ex_date = all_op_ex_dates[all_op_ex_dates >= date][0]
        next_fu_ex_date = all_fu_ex_dates[all_fu_ex_dates >= date][0]

        whether_inbetween = False if next_fu_ex_date < next_op_ex_date else True

        if whether_adjust:
            if auto_compute_zero:
                if not zero_exist or date in all_etf_dividend_dates:
                    op_kind_dict = {
                        '510050': ('IH', '000016', '50ETF', 30, 1),
                        '510300': ('IF', '000300', '300ETF', 30, 1),
                        '159919': ('IF', '000300', '919ETF', 30, 1),
                        '510500': ('IC', '000905', '500ETF', 20, 1),
                        '512100': ('IM', '000852', '1000ETF', 50, 2.5),
                    }
                    denominator = op_kind_dict[op_kind][3]
                    etf_price = pickle_read_md1d_s(op_kind).loc[str(date)].pre_close * 1000
                    index_price = pickle_read_md1d_i(op_kind_dict[op_kind][1]).loc[str(date)].pre_close
                    zero_multiplier_ext = (index_price / (etf_price * op_kind_dict[op_kind][4])) * denominator
                    zero_multiplier_dec = zero_multiplier_ext - int(zero_multiplier_ext)
                    dec_choices = np.array([0., 0.2, 0.25, 0.33, 0.5, 0.67, 0.75, 0.8, 1.])
                    dec_choice = dec_choices[np.argmin(np.abs(zero_multiplier_dec - dec_choices))]
                    zero_multiplier = (int((int(zero_multiplier_ext) + dec_choice) * 100 + 1e-8) / 100) * \
                                       op_kind_dict[op_kind][4] / denominator
                    zero_exist = True 
        else:
            zero_multiplier = 1

        if date in all_fu_ex_dates:
            zero_exist = False

        if curr_month == today_month:
            zero_multiplier = curr_multiplier[f_kind]

        if curr_month_f[-2:] in ['02', '05', '08', '11']:
            daily_zero_inbetween = get_zero(f_kind, 2, date, zero_multiplier)
        else:
            daily_zero_inbetween = get_zero(f_kind, 3, date, zero_multiplier)

        daily_zero_normal = get_zero(f_kind, 3, date, zero_multiplier)

        if curr_month not in a51['abs']:
            a51['abs'][curr_month] = []
        if curr_month not in a51['rel']:
            a51['rel'][curr_month] = []
        if curr_month not in a51['b']:
            a51['b'][curr_month] = []

        if not whether_inbetween:

            daily_a51, b = get_daily_a51(op_kind, f_kind, 3, 3, date, zero_multiplier)

            a51['abs'][curr_month].append(daily_a51)

            a51['rel'][curr_month].append(daily_a51 - daily_zero_normal)

            a51['b'][curr_month].append(b)

        else:

            if curr_month_f[-2:] in ['02', '05', '08', '11']:
                daily_a51, b = get_daily_a51(op_kind, f_kind, 3, 2, date, zero_multiplier)
            else:
                daily_a51, b = get_daily_a51(op_kind, f_kind, 3, 3, date, zero_multiplier)

            a51['abs'][curr_month].append(daily_a51)

            a51['rel'][curr_month].append(daily_a51 - daily_zero_inbetween)

            a51['b'][curr_month].append(b)

    for month in a51['abs'].keys():
        a51['abs'][month] = pd.concat(a51['abs'][month])

    for month in a51['rel'].keys():
        a51['rel'][month] = pd.concat(a51['rel'][month])

    for month in a51['b'].keys():
        a51['b'][month] = pd.concat(a51['b'][month])

    return a51



def get_history_ff(f_kind, f_exp1, f_exp2, starting_date, ending_date):
    starting_date = datetime.datetime.strptime(str(starting_date), '%Y-%m-%d').date()
    ending_date = datetime.datetime.strptime(str(ending_date), '%Y-%m-%d').date()

    ff = {'abs': {}, 'rel': {}, 'b': {}}

    all_t_days = get_all_tradedays(starting_date, ending_date)

    for date in tqdm(all_t_days[:]):

        f_zero1 = get_zero(f_kind=f_kind, f_exp=f_exp1, date=date).mean()

        f_zero2 = get_zero(f_kind=f_kind, f_exp=f_exp2, date=date).mean()

        f_zero = f_zero2 - f_zero1

        curr_month = get_month(False, True, None, f_kind, date)

        if curr_month not in ff['abs']:
            ff['abs'][curr_month] = []
        if curr_month not in ff['rel']:
            ff['rel'][curr_month] = []
        if curr_month not in ff['b']:
            ff['b'][curr_month] = []

        daily_ff, b = get_daily_ff(f_kind, f_exp1, f_exp2, date)

        ff['abs'][curr_month].append(daily_ff)

        ff['rel'][curr_month].append(daily_ff + f_zero)

        ff['b'][curr_month].append(b)

    for month in ff['abs'].keys():
        ff['abs'][month] = pd.concat(ff['abs'][month])

    for month in ff['rel'].keys():
        ff['rel'][month] = pd.concat(ff['rel'][month])

    for month in ff['b'].keys():
        ff['b'][month] = pd.concat(ff['b'][month])

    return ff


def get_history_opop(op_kind, op_exp1, op_exp2, starting_date, ending_date):
    starting_date = datetime.datetime.strptime(str(starting_date), '%Y-%m-%d').date()
    ending_date = datetime.datetime.strptime(str(ending_date), '%Y-%m-%d').date()

    opop = {'abs': {}, 'b': {}}

    all_t_days = get_all_tradedays(starting_date, ending_date)

    for date in tqdm(all_t_days[:]):

        # print(date)

        curr_month = get_month(True, False, op_kind, None, date)

        if curr_month not in opop['abs']:
            opop['abs'][curr_month] = []

        if curr_month not in opop['b']:
            opop['b'][curr_month] = []

        daily_opop, b = get_daily_opop(op_kind, op_exp1, op_exp2, date)

        opop['abs'][curr_month].append(daily_opop)

        opop['b'][curr_month].append(b)

    for month in opop['abs'].keys():
        opop['abs'][month] = pd.concat(opop['abs'][month])

    for month in opop['b'].keys():
        opop['b'][month] = pd.concat(opop['b'][month])

    return opop


def get_history_a55(op_kind1, op_kind2, op_exp1, op_exp2, starting_date, ending_date, multiplier=None):
    starting_date = datetime.datetime.strptime(str(starting_date), '%Y-%m-%d').date()
    ending_date = datetime.datetime.strptime(str(ending_date), '%Y-%m-%d').date()

    auto_zero = True if not multiplier else False

    a55 = {'abs': {}, 'b': {}}

    all_t_days = get_all_tradedays(starting_date, ending_date)

    m_record = 1e8

    for date in tqdm(all_t_days[:]):

        # print(date)

        curr_month = get_month(True, False, op_kind1, None, date)

        if auto_zero:
            asset_price1 = pickle_read_md1m_s(op_kind1).loc[str(date)].close.mean()
            asset_price2 = pickle_read_md1m_s(op_kind2).loc[str(date)].close.mean()
            multiplier_ = round(asset_price1 / asset_price2, 3)

            if abs(multiplier_ - m_record) > 0.01:
                multiplier = multiplier_

            m_record = multiplier

        if curr_month not in a55['abs']:
            a55['abs'][curr_month] = []

        if curr_month not in a55['b']:
            a55['b'][curr_month] = []

        daily_a55, b = get_daily_a55(op_kind1, op_kind2, op_exp1, op_exp2, 1, multiplier, date)

        a55['abs'][curr_month].append(daily_a55)

        a55['b'][curr_month].append(b)

    for month in a55['abs'].keys():
        a55['abs'][month] = pd.concat(a55['abs'][month])

    for month in a55['b'].keys():
        a55['b'][month] = pd.concat(a55['b'][month])

    return a55


def get_history_a33(etf_kind1, etf_kind2, starting_date, ending_date, etf_multiplier=None):
    starting_date = datetime.datetime.strptime(str(starting_date), '%Y-%m-%d').date()
    ending_date = datetime.datetime.strptime(str(ending_date), '%Y-%m-%d').date()

    auto_zero = True if not etf_multiplier else False

    a33 = {'abs': {}, 'b': {}}

    all_t_days = get_all_tradedays(starting_date, ending_date)

    m_record = 1e8

    for date in tqdm(all_t_days[:]):

        try:
            curr_month = get_month(True, False, etf_kind1, None, date)
        except:
            curr_month = get_month(True, False, etf_kind2, None, date)

        if auto_zero:
            asset_price1 = pickle_read_md1m_s(etf_kind1).loc[str(date)].close.mean()
            asset_price2 = pickle_read_md1m_s(etf_kind2).loc[str(date)].close.mean()
            multiplier_ = round(asset_price1 / asset_price2, 3)

            if abs(multiplier_ - m_record) > 0.01:
                etf_multiplier = multiplier_

            m_record = etf_multiplier

        # print(etf_multiplier)

        if curr_month not in a33['abs']:
            a33['abs'][curr_month] = []
        if curr_month not in a33['b']:
            a33['b'][curr_month] = []

        daily_a33, b = get_daily_a33(etf_kind1, etf_kind2, date, etf_multiplier)

        a33['abs'][curr_month].append(daily_a33)
        a33['b'][curr_month].append(b)

    for month in a33['abs'].keys():
        a33['abs'][month] = pd.concat(a33['abs'][month])

    for month in a33['b'].keys():
        a33['b'][month] = pd.concat(a33['b'][month])

    return a33


def get_history_a61(f_kind, starting_date, ending_date):
    starting_date = datetime.datetime.strptime(str(starting_date), '%Y-%m-%d').date()
    ending_date = datetime.datetime.strptime(str(ending_date), '%Y-%m-%d').date()

    a61 = {'abs': {}, 'b': {}}

    all_t_days = get_all_tradedays(starting_date, ending_date)

    for date in tqdm(all_t_days[:]):
        a61_data, b = get_daily_a61(date=date, f_kind=f_kind)

        a61['abs'][str(date)] = a61_data
        a61['b'][str(date)] = b

    return a61



a5h = get_history_a5(op_kind='510050', etf_kind='510050', op_exp=0, starting_date=datetime.date(2020,1,1), ending_date=today)
na5h = get_history_a5(op_kind='510050', etf_kind='510050', op_exp=1, starting_date=datetime.date(2020,1,1), ending_date=today)
sa5h = get_history_a5(op_kind='510050', etf_kind='510050', op_exp=2, starting_date=datetime.date(2020,1,1), ending_date=today)

a5f = get_history_a5(op_kind='510300', etf_kind='510300', op_exp=0, starting_date=datetime.date(2020,1,1), ending_date=today)
na5f = get_history_a5(op_kind='510300', etf_kind='510300', op_exp=1, starting_date=datetime.date(2020,1,1), ending_date=today)
sa5f = get_history_a5(op_kind='510300', etf_kind='510300', op_exp=2, starting_date=datetime.date(2020,1,1), ending_date=today)
a5f_919 = get_history_a5(op_kind='159919', etf_kind='159919', op_exp=0, starting_date=datetime.date(2020,1,1), ending_date=today)
na5f_919 = get_history_a5(op_kind='159919', etf_kind='159919', op_exp=1, starting_date=datetime.date(2020,1,1), ending_date=today)
a5f_300_919 = get_history_a5(op_kind='510300', etf_kind='159919', op_exp=0, starting_date=datetime.date(2020,1,1), ending_date=today)
a5f_919_300 = get_history_a5(op_kind='159919', etf_kind='510300', op_exp=0, starting_date=datetime.date(2020,1,1), ending_date=today)
a5f_300_310 = get_history_a5(op_kind='510300', etf_kind='510310', op_exp=0, starting_date=datetime.date(2020,1,1), ending_date=today)
a5f_300_330 = get_history_a5(op_kind='510300', etf_kind='510330', op_exp=0, starting_date=datetime.date(2020,1,1), ending_date=today)
a5_300_a500 = get_history_a5(op_kind='510300', etf_kind='159338', op_exp=0, starting_date=datetime.date(2024,10,16), ending_date=today)

a5c = get_history_a5(op_kind='510500', etf_kind='510500', op_exp=0, starting_date=datetime.date(2022,10,27), ending_date=today)
na5c = get_history_a5(op_kind='510500', etf_kind='510500', op_exp=1, starting_date=datetime.date(2022,10,27), ending_date=today)
sa5c = get_history_a5(op_kind='510500', etf_kind='510500', op_exp=2, starting_date=datetime.date(2022,10,27), ending_date=today)
a5c_922 = get_history_a5(op_kind='159922', etf_kind='159922', op_exp=0, starting_date=datetime.date(2022,10,27), ending_date=today)
na5c_922 = get_history_a5(op_kind='159922', etf_kind='159922', op_exp=1, starting_date=datetime.date(2022,10,27), ending_date=today)

a5y = get_history_a5(op_kind='159915', etf_kind='159915', op_exp=0, starting_date=datetime.date(2022,10,27), ending_date=today)
na5y = get_history_a5(op_kind='159915', etf_kind='159915', op_exp=1, starting_date=datetime.date(2022,10,27), ending_date=today)
sa5y = get_history_a5(op_kind='159915', etf_kind='159915', op_exp=2, starting_date=datetime.date(2022,10,27), ending_date=today)

a5k = get_history_a5(op_kind='588000', etf_kind='588000', op_exp=0, starting_date=datetime.date(2023,6,15), ending_date=today)
na5k = get_history_a5(op_kind='588000', etf_kind='588000', op_exp=1, starting_date=datetime.date(2023,6,15), ending_date=today)
sa5k = get_history_a5(op_kind='588000', etf_kind='588000', op_exp=2, starting_date=datetime.date(2023,6,15), ending_date=today)

a5k_k58 = get_history_a5(op_kind='588080', etf_kind='588080', op_exp=0, starting_date=datetime.date(2023,6,15), ending_date=today)
na5k_k58 = get_history_a5(op_kind='588080', etf_kind='588080', op_exp=1, starting_date=datetime.date(2023,6,15), ending_date=today)

a5k_k50_k58 = get_history_a5(op_kind='588000', etf_kind='588080', op_exp=0, starting_date=datetime.date(2023,6,15), ending_date=today)
a5k_k58_k50 = get_history_a5(op_kind='588080', etf_kind='588000', op_exp=0, starting_date=datetime.date(2023,6,15), ending_date=today)

a13h = get_history_a13(etf_kind='510050', f_kind='IH', f_exp=0, starting_date=datetime.date(2020,1,1), ending_date=datetime.datetime.now().date())
na13h = get_history_a13(etf_kind='510050', f_kind='IH', f_exp=1, starting_date=datetime.date(2020,1,1), ending_date=datetime.datetime.now().date())
sa13h = get_history_a13(etf_kind='510050', f_kind='IH', f_exp=2, starting_date=datetime.date(2020,1,1), ending_date=datetime.datetime.now().date())

a13f = get_history_a13(etf_kind='510300', f_kind='IF', f_exp=0, starting_date=datetime.date(2020,1,1), ending_date=datetime.datetime.now().date())
na13f = get_history_a13(etf_kind='510300', f_kind='IF', f_exp=1, starting_date=datetime.date(2020,1,1), ending_date=datetime.datetime.now().date())
sa13f = get_history_a13(etf_kind='510300', f_kind='IF', f_exp=2, starting_date=datetime.date(2020,1,1), ending_date=datetime.datetime.now().date())

a13c = get_history_a13(etf_kind='510500', f_kind='IC', f_exp=0, starting_date=datetime.date(2020,1,1), ending_date=datetime.datetime.now().date())
na13c = get_history_a13(etf_kind='510500', f_kind='IC', f_exp=1, starting_date=datetime.date(2020,1,1), ending_date=datetime.datetime.now().date())
sa13c = get_history_a13(etf_kind='510500', f_kind='IC', f_exp=2, starting_date=datetime.date(2020,1,1), ending_date=datetime.datetime.now().date())

a13m = get_history_a13(etf_kind='512100', f_kind='IM', f_exp=0, starting_date=datetime.date(2023,1,1), ending_date=datetime.datetime.now().date())
na13m = get_history_a13(etf_kind='512100', f_kind='IM', f_exp=1, starting_date=datetime.date(2023,1,1), ending_date=datetime.datetime.now().date())
sa13m = get_history_a13(etf_kind='512100', f_kind='IM', f_exp=2, starting_date=datetime.date(2023,1,1), ending_date=datetime.datetime.now().date())

a51h = get_history_a51(op_kind='510050', f_kind='IH', starting_date=datetime.date(2020,1,1), ending_date=today)
ca51h = get_history_ca51(op_kind='510050', f_kind='IH', starting_date=datetime.date(2020,1,1), ending_date=today)
nca51h = get_history_nca51(op_kind='510050', f_kind='IH', starting_date=datetime.date(2020,1,1), ending_date=today)
ka51h = get_history_ka51(op_kind='510050', f_kind='IH', starting_date=datetime.date(2020,1,1), ending_date=today)
na51h = get_history_na51(op_kind='510050', f_kind='IH', starting_date=datetime.date(2020,1,1), ending_date=today)

a51f = get_history_a51(op_kind='510300', f_kind='IF', starting_date=datetime.date(2020,1,1), ending_date=today)
ca51f = get_history_ca51(op_kind='510300', f_kind='IF', starting_date=datetime.date(2020,1,1), ending_date=today)
nca51f = get_history_nca51(op_kind='510300', f_kind='IF', starting_date=datetime.date(2020,1,1), ending_date=today)
ka51f = get_history_ka51(op_kind='510300', f_kind='IF', starting_date=datetime.date(2020,1,1), ending_date=today)
na51f = get_history_na51(op_kind='510300', f_kind='IF', starting_date=datetime.date(2020,1,1), ending_date=today)

a51c = get_history_a51(op_kind='510500', f_kind='IC', starting_date=datetime.date(2022,10,27), ending_date=today)
ca51c = get_history_ca51(op_kind='510500', f_kind='IC', starting_date=datetime.date(2022,10,27), ending_date=today)
nca51c = get_history_nca51(op_kind='510500', f_kind='IC', starting_date=datetime.date(2022,10,27), ending_date=today)
ka51c = get_history_ka51(op_kind='510500', f_kind='IC', starting_date=datetime.date(2022,10,27), ending_date=today)
na51c = get_history_na51(op_kind='510500', f_kind='IC', starting_date=datetime.date(2022,10,27), ending_date=today)

opop_50 = get_history_opop(op_kind='510050', op_exp1=0, op_exp2=1, starting_date=datetime.date(2020,1,1), ending_date=today)
opop_n50 = get_history_opop(op_kind='510050', op_exp1=0, op_exp2=2, starting_date=datetime.date(2020,1,1), ending_date=today)

opop_300 = get_history_opop(op_kind='510300', op_exp1=0, op_exp2=1, starting_date=datetime.date(2020,1,1), ending_date=today)
opop_n300 = get_history_opop(op_kind='510300', op_exp1=0, op_exp2=2, starting_date=datetime.date(2020,1,1), ending_date=today)

opop_500 = get_history_opop(op_kind='510500', op_exp1=0, op_exp2=1, starting_date=datetime.date(2022,10,27), ending_date=today)
opop_n500 = get_history_opop(op_kind='510500', op_exp1=0, op_exp2=2, starting_date=datetime.date(2022,10,27), ending_date=today)

opop_915 = get_history_opop(op_kind='159915', op_exp1=0, op_exp2=1, starting_date=datetime.date(2022,10,27), ending_date=today)
opop_n915 = get_history_opop(op_kind='159915', op_exp1=0, op_exp2=2, starting_date=datetime.date(2022,10,27), ending_date=today)

opop_k50 = get_history_opop(op_kind='588000', op_exp1=0, op_exp2=1, starting_date=datetime.date(2023,6,15), ending_date=today)
opop_nk50 = get_history_opop(op_kind='588000', op_exp1=0, op_exp2=2, starting_date=datetime.date(2023,6,15), ending_date=today)

opop_k58 = get_history_opop(op_kind='588080', op_exp1=0, op_exp2=1, starting_date=datetime.date(2023,6,15), ending_date=today)
opop_nk58 = get_history_opop(op_kind='588080', op_exp1=0, op_exp2=2, starting_date=datetime.date(2023,6,15), ending_date=today)

ff_ih = get_history_ff(f_kind='IH', f_exp1=0, f_exp2=1, starting_date=datetime.date(2020,1,1), ending_date=today)
ff_if = get_history_ff(f_kind='IF', f_exp1=0, f_exp2=1, starting_date=datetime.date(2020,1,1), ending_date=today)
ff_ic = get_history_ff(f_kind='IC', f_exp1=0, f_exp2=1, starting_date=datetime.date(2020,1,1), ending_date=today)
ff_im = get_history_ff(f_kind='IM', f_exp1=0, f_exp2=1, starting_date=datetime.date(2022,10,27), ending_date=today)

ff_nih = get_history_ff(f_kind='IH', f_exp1=0, f_exp2=2, starting_date=datetime.date(2020,1,1), ending_date=today)
ff_nif = get_history_ff(f_kind='IF', f_exp1=0, f_exp2=2, starting_date=datetime.date(2020,1,1), ending_date=today)
ff_nic = get_history_ff(f_kind='IC', f_exp1=0, f_exp2=2, starting_date=datetime.date(2020,1,1), ending_date=today)
ff_nim = get_history_ff(f_kind='IM', f_exp1=0, f_exp2=2, starting_date=datetime.date(2022,10,27), ending_date=today)

ff_sih = get_history_ff(f_kind='IH', f_exp1=0, f_exp2=3, starting_date=datetime.date(2020,1,1), ending_date=today)
ff_sif = get_history_ff(f_kind='IF', f_exp1=0, f_exp2=3, starting_date=datetime.date(2020,1,1), ending_date=today)
ff_sic = get_history_ff(f_kind='IC', f_exp1=0, f_exp2=3, starting_date=datetime.date(2020,1,1), ending_date=today)
ff_sim = get_history_ff(f_kind='IM', f_exp1=0, f_exp2=3, starting_date=datetime.date(2022,10,27), ending_date=today)

a55_k50_k58 = get_history_a55(op_kind1='588000', op_kind2='588080', op_exp1=0, op_exp2=0, starting_date=datetime.date(2023,6,15), ending_date=today)
a55_300_919 = get_history_a55(op_kind1='510300', op_kind2='159919', op_exp1=0, op_exp2=0, starting_date=datetime.date(2020,1,1), ending_date=today)
a55_500_922 = get_history_a55(op_kind1='510500', op_kind2='159922', op_exp1=0, op_exp2=0, starting_date=datetime.date(2023,1,1), ending_date=today)

a33_300_919 = get_history_a33(etf_kind1='510300', etf_kind2='159919', starting_date=datetime.date(2020,1,1), ending_date=datetime.date(2025,3,21))
a33_300_310 = get_history_a33(etf_kind1='510300', etf_kind2='510310', starting_date=datetime.date(2020,1,1), ending_date=datetime.date(2025,3,21))
a33_300_330 = get_history_a33(etf_kind1='510300', etf_kind2='510330', starting_date=datetime.date(2020,1,1), ending_date=datetime.date(2025,3,21))

a61_au = get_history_a61(f_kind='AU', starting_date=datetime.date(2024,1,1), ending_date=today)
a61_cu = get_history_a61(f_kind='CU', starting_date=datetime.date(2024,1,1), ending_date=today)
a61_sc = get_history_a61(f_kind='SC', starting_date=datetime.date(2024,1,1), ending_date=today)




with open('/A/MD/A_history/a5h.pkl', 'wb') as file:
    pickle.dump(a5h, file)

with open('/A/MD/A_history/na5h.pkl', 'wb') as file:
    pickle.dump(na5h, file)

with open('/A/MD/A_history/sa5h.pkl', 'wb') as file:
    pickle.dump(sa5h, file)

with open('/A/MD/A_history/a5f.pkl', 'wb') as file:
    pickle.dump(a5f, file)

with open('/A/MD/A_history/na5f.pkl', 'wb') as file:
    pickle.dump(na5f, file)

with open('/A/MD/A_history/sa5f.pkl', 'wb') as file:
    pickle.dump(sa5f, file)

with open('/A/MD/A_history/a5f_919.pkl', 'wb') as file:
    pickle.dump(a5f_919, file)

with open('/A/MD/A_history/na5f_919.pkl', 'wb') as file:
    pickle.dump(na5f_919, file)

with open('/A/MD/A_history/a5f_300_919.pkl', 'wb') as file:
    pickle.dump(a5f_300_919, file)

with open('/A/MD/A_history/a5f_919_300.pkl', 'wb') as file:
    pickle.dump(a5f_919_300, file)

with open('/A/MD/A_history/a5f_300_310.pkl', 'wb') as file:
    pickle.dump(a5f_300_310, file)

with open('/A/MD/A_history/a5f_300_330.pkl', 'wb') as file:
    pickle.dump(a5f_300_330, file)

with open('/A/MD/A_history/a5_300_a500.pkl', 'wb') as file:
    pickle.dump(a5_300_a500, file)

with open('/A/MD/A_history/a5c.pkl', 'wb') as file:
    pickle.dump(a5c, file)

with open('/A/MD/A_history/na5c.pkl', 'wb') as file:
    pickle.dump(na5c, file)

with open('/A/MD/A_history/sa5c.pkl', 'wb') as file:
    pickle.dump(sa5c, file)

with open('/A/MD/A_history/a5c_922.pkl', 'wb') as file:
    pickle.dump(a5c_922, file)

with open('/A/MD/A_history/na5c_922.pkl', 'wb') as file:
    pickle.dump(na5c_922, file)

with open('/A/MD/A_history/a5y.pkl', 'wb') as file:
    pickle.dump(a5y, file)

with open('/A/MD/A_history/na5y.pkl', 'wb') as file:
    pickle.dump(na5y, file)

with open('/A/MD/A_history/sa5y.pkl', 'wb') as file:
    pickle.dump(sa5y, file)

with open('/A/MD/A_history/a5k.pkl', 'wb') as file:
    pickle.dump(a5k, file)

with open('/A/MD/A_history/na5k.pkl', 'wb') as file:
    pickle.dump(na5k, file)

with open('/A/MD/A_history/sa5k.pkl', 'wb') as file:
    pickle.dump(sa5k, file)

with open('/A/MD/A_history/a5k_k58.pkl', 'wb') as file:
    pickle.dump(a5k_k58, file)

with open('/A/MD/A_history/na5k_k58.pkl', 'wb') as file:
    pickle.dump(na5k_k58, file)

with open('/A/MD/A_history/a5k_k50_k58.pkl', 'wb') as file:
    pickle.dump(a5k_k50_k58, file)

with open('/A/MD/A_history/a5k_k58_k50.pkl', 'wb') as file:
    pickle.dump(a5k_k58_k50, file)

with open('/A/MD/A_history/a13h.pkl', 'wb') as file:
    pickle.dump(a13h, file)

with open('/A/MD/A_history/na13h.pkl', 'wb') as file:
    pickle.dump(na13h, file)

with open('/A/MD/A_history/sa13h.pkl', 'wb') as file:
    pickle.dump(sa13h, file)

with open('/A/MD/A_history/a13f.pkl', 'wb') as file:
    pickle.dump(a13f, file)

with open('/A/MD/A_history/na13f.pkl', 'wb') as file:
    pickle.dump(na13f, file)

with open('/A/MD/A_history/sa13f.pkl', 'wb') as file:
    pickle.dump(sa13f, file)

with open('/A/MD/A_history/a13c.pkl', 'wb') as file:
    pickle.dump(a13c, file)

with open('/A/MD/A_history/na13c.pkl', 'wb') as file:
    pickle.dump(na13c, file)

with open('/A/MD/A_history/sa13c.pkl', 'wb') as file:
    pickle.dump(sa13c, file)

with open('/A/MD/A_history/a13m.pkl', 'wb') as file:
    pickle.dump(a13m, file)

with open('/A/MD/A_history/na13m.pkl', 'wb') as file:
    pickle.dump(na13m, file)

with open('/A/MD/A_history/sa13m.pkl', 'wb') as file:
    pickle.dump(sa13m, file)

with open('/A/MD/A_history/a51h.pkl', 'wb') as file:
    pickle.dump(a51h, file)

with open('/A/MD/A_history/ca51h.pkl', 'wb') as file:
    pickle.dump(ca51h, file)

with open('/A/MD/A_history/nca51h.pkl', 'wb') as file:
    pickle.dump(nca51h, file)

with open('/A/MD/A_history/ka51h.pkl', 'wb') as file:
    pickle.dump(ka51h, file)

with open('/A/MD/A_history/na51h.pkl', 'wb') as file:
    pickle.dump(na51h, file)

with open('/A/MD/A_history/a51f.pkl', 'wb') as file:
    pickle.dump(a51f, file)

with open('/A/MD/A_history/ca51f.pkl', 'wb') as file:
    pickle.dump(ca51f, file)

with open('/A/MD/A_history/nca51f.pkl', 'wb') as file:
    pickle.dump(nca51f, file)

with open('/A/MD/A_history/ka51f.pkl', 'wb') as file:
    pickle.dump(ka51f, file)

with open('/A/MD/A_history/na51f.pkl', 'wb') as file:
    pickle.dump(na51f, file)

with open('/A/MD/A_history/a51c.pkl', 'wb') as file:
    pickle.dump(a51c, file)

with open('/A/MD/A_history/ca51c.pkl', 'wb') as file:
    pickle.dump(ca51c, file)

with open('/A/MD/A_history/nca51c.pkl', 'wb') as file:
    pickle.dump(nca51c, file)

with open('/A/MD/A_history/ka51c.pkl', 'wb') as file:
    pickle.dump(ka51c, file)

with open('/A/MD/A_history/na51c.pkl', 'wb') as file:
    pickle.dump(na51c, file)

with open('/A/MD/A_history/opop_50.pkl', 'wb') as file:
    pickle.dump(opop_50, file)

with open('/A/MD/A_history/opop_n50.pkl', 'wb') as file:
    pickle.dump(opop_n50, file)

with open('/A/MD/A_history/opop_300.pkl', 'wb') as file:
    pickle.dump(opop_300, file)

with open('/A/MD/A_history/opop_n300.pkl', 'wb') as file:
    pickle.dump(opop_n300, file)

with open('/A/MD/A_history/opop_500.pkl', 'wb') as file:
    pickle.dump(opop_500, file)

with open('/A/MD/A_history/opop_n500.pkl', 'wb') as file:
    pickle.dump(opop_n500, file)

with open('/A/MD/A_history/opop_915.pkl', 'wb') as file:
    pickle.dump(opop_915, file)

with open('/A/MD/A_history/opop_n915.pkl', 'wb') as file:
    pickle.dump(opop_n915, file)

with open('/A/MD/A_history/opop_k50.pkl', 'wb') as file:
    pickle.dump(opop_k50, file)

with open('/A/MD/A_history/opop_nk50.pkl', 'wb') as file:
    pickle.dump(opop_nk50, file)

with open('/A/MD/A_history/opop_k58.pkl', 'wb') as file:
    pickle.dump(opop_k58, file)

with open('/A/MD/A_history/opop_nk58.pkl', 'wb') as file:
    pickle.dump(opop_nk58, file)

with open('/A/MD/A_history/ff_ih.pkl', 'wb') as file:
    pickle.dump(ff_ih, file)

with open('/A/MD/A_history/ff_if.pkl', 'wb') as file:
    pickle.dump(ff_if, file)

with open('/A/MD/A_history/ff_ic.pkl', 'wb') as file:
    pickle.dump(ff_ic, file)

with open('/A/MD/A_history/ff_im.pkl', 'wb') as file:
    pickle.dump(ff_im, file)

with open('/A/MD/A_history/ff_nih.pkl', 'wb') as file:
    pickle.dump(ff_nih, file)

with open('/A/MD/A_history/ff_nif.pkl', 'wb') as file:
    pickle.dump(ff_nif, file)

with open('/A/MD/A_history/ff_nic.pkl', 'wb') as file:
    pickle.dump(ff_nic, file)

with open('/A/MD/A_history/ff_nim.pkl', 'wb') as file:
    pickle.dump(ff_nim, file)

with open('/A/MD/A_history/ff_sih.pkl', 'wb') as file:
    pickle.dump(ff_sih, file)

with open('/A/MD/A_history/ff_sif.pkl', 'wb') as file:
    pickle.dump(ff_sif, file)

with open('/A/MD/A_history/ff_sic.pkl', 'wb') as file:
    pickle.dump(ff_sic, file)

with open('/A/MD/A_history/ff_sim.pkl', 'wb') as file:
    pickle.dump(ff_sim, file)

with open('/A/MD/A_history/a55_k50_k58.pkl', 'wb') as file:
    pickle.dump(a55_k50_k58, file)

with open('/A/MD/A_history/a55_300_919.pkl', 'wb') as file:
    pickle.dump(a55_300_919, file)

with open('/A/MD/A_history/a55_500_922.pkl', 'wb') as file:
    pickle.dump(a55_500_922, file)

with open('/A/MD/A_history/a33_300_919.pkl', 'wb') as file:
    pickle.dump(a33_300_919, file)

with open('/A/MD/A_history/a33_300_310.pkl', 'wb') as file:
    pickle.dump(a33_300_310, file)

with open('/A/MD/A_history/a33_300_330.pkl', 'wb') as file:
    pickle.dump(a33_300_330, file)

with open('/A/MD/A_history/a61_au.pkl', 'wb') as file:
    pickle.dump(a61_au, file)

with open('/A/MD/A_history/a61_cu.pkl', 'wb') as file:
    pickle.dump(a61_cu, file)

with open('/A/MD/A_history/a61_sc.pkl', 'wb') as file:
    pickle.dump(a61_sc, file)

