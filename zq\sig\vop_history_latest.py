from nb_common import *
from scipy.optimize import newton
from scipy.stats import norm
import os
import calendar
import math
from joblib import Parallel, delayed
from tqdm.notebook import tqdm
from scipy.optimize import minimize
from ut_redis import FrRedis

rd170 = FrRedis("************")

pd.set_option('display.max_rows', 1000)
pd.set_option('display.max_columns', 1000)

jq.auth("13051586300", "Frtz8888")
print(jq.get_query_count())

jq.auth("18601177671", "Frtz8888") 
print(jq.get_query_count())


def get_all_op_ex_dates():
    full_info = pd.read_pickle(f'{MDLSTDIR}/all_securities/full.pickle')
    all_options_ex_dates = sorted(
        full_info[(full_info.type == 'options') & (full_info.name.str[:2] == '10')].end_date.dt.strftime(
            "%Y-%m-%d").unique())
    all_options_ex_dates = np.array(
        [datetime.datetime.strptime(date, "%Y-%m-%d").date() for date in all_options_ex_dates])

    return all_options_ex_dates


def get_hvop(vop, nvop):
    
    hvop = {'jz':{} ,'tv':{}, 'margin':{}}
    for mon in vop['jz'].keys():
        hvop['jz'][mon] = vop['jz'][mon] - nvop['jz'][mon]
        hvop['tv'][mon] = vop['tv'][mon]
        hvop['margin'][mon] = vop['margin'][mon]['s'] + nvop['margin'][mon]['b']
        
    return hvop


def get_hvop_index(hvops):

    hvop_index = {'jz':{}, 'tv':{}, 'margin':{}}
    mons = hvops[0]['jz'].keys() 
    for mon in mons:
        avg_jz = []
        avg_tv = []
        avg_margin = []
        for hvop in hvops:
            avg_jz.append(hvop['jz'][mon])
            avg_tv.append(hvop['tv'][mon])
            avg_margin.append(hvop['margin'][mon])
        hvop_index['jz'][mon] = pd.concat(avg_jz, axis=1).sum(axis=1)
        hvop_index['tv'][mon] = pd.concat(avg_tv, axis=1).sum(axis=1)
        hvop_index['margin'][mon] = pd.concat(avg_margin, axis=1).sum(axis=1)

    return hvop_index


def get_cross_perf(hvop):
    
    cross_mon = {'jz':[], 'tv':[], 'margin':[]}
    for mon in hvop['jz'].keys():
        cross_mon['jz'].append( np.concatenate((np.zeros(360000-hvop['jz'][mon].values.flatten().shape[0]), hvop['jz'][mon].values.flatten())).reshape(-1,1) )
        cross_mon['tv'].append( np.concatenate((np.zeros(360000-hvop['tv'][mon].values.flatten().shape[0]), hvop['tv'][mon].values.flatten())).reshape(-1,1)  )
        cross_mon['margin'].append( np.concatenate((np.zeros(360000-hvop['margin'][mon].values.flatten().shape[0]), hvop['margin'][mon].values.flatten())).reshape(-1,1)  )
    cross_mon['jz'] = np.concatenate(cross_mon['jz'], axis=1).mean(axis=1)
    cross_mon['tv'] = np.concatenate(cross_mon['tv'], axis=1).mean(axis=1)
    cross_mon['margin'] = np.concatenate(cross_mon['margin'], axis=1).mean(axis=1)
    
    return cross_mon



def get_last_exdt(date):
    all_t_days = np.array(pickle_read_tdays())
    # full_info = pd.read_pickle(f'{MDLSTDIR}/all_securities/full.pickle')
    all_options_ex_dates = sorted(full_info[(full_info.type == 'options') & (full_info.name.str[:2] == '10')].end_date.dt.strftime("%Y-%m-%d").unique())
    all_options_ex_dates = np.array([datetime.datetime.strptime(date, "%Y-%m-%d").date() for date in all_options_ex_dates]) 
    last_exdt = [int(c) for c in str(np.sort(all_options_ex_dates[all_options_ex_dates < date])[-1]).split('-')]
    last_exdt = datetime.date(last_exdt[0],last_exdt[1],last_exdt[2])

    last_exdt_next = all_t_days[np.argwhere(all_t_days==last_exdt)[0][0]+1]
    
    return last_exdt_next



def save_data_to_server33(underlying, type, data, st_mon=None):

    if not st_mon:
        st_mon = 0
    
    for mon in tqdm(data['jz'].keys()):
        if int(mon) > int(st_mon):
            mon_data = data['jz'][mon]
            mon_data.to_csv('/A/MD/vop_history/monthly/'+type+'/'+underlying+'/'+mon+'.csv')
    
            for r in range(0, mon_data.shape[0], 14400):
                daily_data = mon_data.iloc[r:r+14400,:]
                filename = str(daily_data.index[0]).split()[0]
                daily_data.to_csv('/A/MD/vop_history/daily/'+type+'/'+underlying+'/'+filename+'.csv')



def get_month(date):

    # full_info = pd.read_pickle(f'{MDLSTDIR}/all_securities/full.pickle')
    all_options_ex_dates = sorted(full_info[(full_info.type == 'options') & (full_info.name.str[:2] == '10')].end_date.dt.strftime("%Y-%m-%d").unique())
    all_options_ex_dates = np.array([datetime.datetime.strptime(date, "%Y-%m-%d").date() for date in all_options_ex_dates]) 
    next_ex_dt = str(np.sort(all_options_ex_dates[all_options_ex_dates >= date])[0]).split('-')
    next_ex_dt = next_ex_dt[0][-2:] + next_ex_dt[1]

    return next_ex_dt
    



def get_time_value(op_price, etf_price, strike, op_type):

    if op_type == 1:

        intrinsic_value = max(etf_price - strike, 0) * 1e4

    elif op_type == 2:

        intrinsic_value = max(strike - etf_price, 0) * 1e4

    time_value = op_price - intrinsic_value

    return time_value/100




def get_margin(op_price, etf_price, strike, op_type, direction):

    con_tv = get_time_value(op_price, etf_price, strike, op_type) * 1e-2

    if direction == 'b':

        margin = op_price

    elif direction == 's':

        if op_type == 1:

            margin = (op_price*1e-4 + max(0.12 * etf_price - con_tv, 0.07 * etf_price)) * 10000

        else:

            margin = min(op_price*1e-4 + max(0.12 * etf_price - con_tv, 0.07 * strike), strike) * 10000

    return margin



def get_option_positions(c1_delta, c2_delta, p1_delta, p2_delta, \
                         c1_price, c2_price, p1_price, p2_price, \
                         all_cash):

    objective = lambda x: abs(c1_delta*x[0] + c2_delta*x[1] + p1_delta*x[2] + p2_delta*x[3]) 
    
    cons = ({'type': 'ineq', 'fun': lambda x: (c1_price*x[0] + c2_price*x[1] + p1_price*x[2] + p2_price*x[3]) - all_cash}, 
            {'type': 'ineq', 'fun': lambda x: (x[0]-x[1])}, 
            {'type': 'ineq', 'fun': lambda x: (x[2]-x[3])}) 
    
    bnds = ((0, None), (0, None), (0, None), (0, None))
    
    res = minimize(objective, (0, 0, 0, 0), bounds=bnds,
                   constraints=cons)['x']

    if res.max() > 30:
        res = min(30/res.max(), 1) * res

    if sum(res) > 100:
        adj_res = (100 / sum(res)) * res
    else:
        adj_res = res

    adj_res = (adj_res * 10).astype(int) * 0.1
    
    holding_asset = (c1_price*adj_res[0] + c2_price*adj_res[1] + p1_price*adj_res[2] + p2_price*adj_res[3])
    
    if holding_asset > all_cash:
        print('Asset Used:', holding_asset, 'All Available:', all_cash)
        adj_res = (all_cash / (holding_asset+100)) * adj_res 
        adj_res = (adj_res * 10).astype(int) * 0.1

    return adj_res

    

def get_option_contracts(etf_price, underlying, date, contract_month, t):

    mdi = pickle_read_mdi_A(str(date))
    
    curr_month = mdi.opkind[underlying]['a_yymm'][contract_month]
    all_k =  np.array(mdi.opkind[underlying]['d_yymm_pxu_M'][curr_month])
    all_k_arr = np.array([int(con[1:]) for con in all_k])
    close_k_arr = all_k[np.argsort(np.abs(all_k_arr - etf_price))][:3]
    
    call_arr = np.array([mdi.oplstt.xs((underlying, curr_month, "M", k)).C[:-5] for k in close_k_arr])
    put_arr = np.array([mdi.oplstt.xs((underlying, curr_month, "M", k)).P[:-5] for k in close_k_arr])
    
    call_delta_arr = []
    put_delta_arr = []
    for c, p in zip(call_arr, put_arr):

        ogc = pd.read_pickle('/A/MD/tick/o/processed/'+underlying+'/'+c+'.pickle')
        ogp = pd.read_pickle('/A/MD/tick/o/processed/'+underlying+'/'+p+'.pickle')

        ogc = ogc[(ogc.index>=str(date)+' 09:00:00') & (ogc.index<=str(date)+' 15:30:00')]
        ogp = ogp[(ogp.index>=str(date)+' 09:00:00') & (ogp.index<=str(date)+' 15:30:00')]
        
        call_delta_arr.append(ogc.delta.iloc[t])
        put_delta_arr.append(ogp.delta.iloc[t])
    
    call1, call2 = call_arr[np.argsort(call_delta_arr)[::-1][-2:]] # delta 0.5 0.2
    put1, put2 = put_arr[np.argsort(put_delta_arr)[-2:]] # delta -0.5, -0.2

    call1_delta, call2_delta = np.array(call_delta_arr)[np.argsort(call_delta_arr)[::-1][-2:]]
    put1_delta, put2_delta = np.array(put_delta_arr)[np.argsort(put_delta_arr)[-2:]]

    if call1_delta < 0.3:
        call1, call2 = call_arr[np.argsort(call_delta_arr)[::-1][:2]]
        put1, put2 = put_arr[np.argsort(put_delta_arr)[-2:]]

    if put1_delta > -0.3:
        call1, call2 = call_arr[np.argsort(call_delta_arr)[::-1][-2:]]
        put1, put2 = put_arr[np.argsort(put_delta_arr)[:2]]
    
    return call1, call2, put1, put2



def get_smooth_adj_schedule(dic1, dic2, num=600):
    smooth_adj = {}
    for con in dic1.keys():
        if con not in dic2:
            dic2[con] = 0
    for con in dic2.keys():
        if con not in dic1:
            dic1[con] = 0
    for con in dic1:
        smooth_adj[con] = np.linspace(dic1[con], dic2[con], num=num+1)
    return smooth_adj


    
    
def compute_daily_signal(daily_c1_delta, daily_c2_delta, daily_p1_delta, daily_p2_delta, \
                         daily_c1_price, daily_c2_price, daily_p1_price, daily_p2_price, \
                         daily_c1_spread, daily_c2_spread, daily_p1_spread, daily_p2_spread, \
                         daily_etf_price, \
                         c1_con, c2_con, p1_con, p2_con, \
                         c1, c2, p1, p2, \
                         c1_k, c2_k, p1_k, p2_k, \
                         remaining_cash, \
                         contract_month, \
                         daily_perc, \
                         date, underlying='510050', all_asset=10000):
    signal = []
    tv = []
    mb, ms = [], []
    con_reb, pos_reb = [], []
    
    rebalance_benchmark = 0
    whether_rebalance = False
    rebalance_complete = True
    rebalance_scehdule = None
    rebalance_counter = 1
    ed_con_dict = None

    timestamps = None
    
    old_c1_con, old_c2_con, old_p1_con, old_p2_con = None, None, None, None
    daily_old_c1_price, daily_old_c2_price, daily_old_p1_price, daily_old_p2_price = None, None, None, None
        
    for t in (range(14400)):

        if not c1_con or t == 0:

            if c1_con is not None and t == 0:
                all_asset = daily_c1_price.iloc[t]*1e4*c1 + daily_c2_price.iloc[t]*1e4*c2 + daily_p1_price.iloc[t]*1e4*p1 + daily_p2_price.iloc[t]*1e4*p2 + remaining_cash
                if daily_c1_delta.iloc[t] > 0.4 and daily_c1_delta.iloc[t] < 0.6 and daily_p1_delta.iloc[t] < -0.4 and daily_p1_delta.iloc[t] > -0.6:
                    c1_con_, c2_con_, p1_con_, p2_con_ = c1_con, c2_con, p1_con, p2_con
                    pos_reb.append(t)
                else:
                    c1_con_, c2_con_, p1_con_, p2_con_ = get_option_contracts(daily_etf_price[t], underlying, date, contract_month, 0)
                    con_reb.append(t)
            else:
                c1_con_, c2_con_, p1_con_, p2_con_ = get_option_contracts(daily_etf_price[t], underlying, date, contract_month, 0)
                con_reb.append(t)
            
            print('Initial Rebalance!')
            
            if not (c1_con == c1_con_ and c2_con == c2_con_ and p1_con == p1_con_ and p2_con == p2_con_):

                c1_con = c1_con_
                c2_con = c2_con_  
                p1_con = p1_con_ 
                p2_con = p2_con_ 
                
                c1_df = pd.read_pickle('/A/MD/tick/o/processed/'+underlying+'/'+c1_con+'.pickle')
                c2_df = pd.read_pickle('/A/MD/tick/o/processed/'+underlying+'/'+c2_con+'.pickle')
                p1_df = pd.read_pickle('/A/MD/tick/o/processed/'+underlying+'/'+p1_con+'.pickle')
                p2_df = pd.read_pickle('/A/MD/tick/o/processed/'+underlying+'/'+p2_con+'.pickle')
                
                c1_df = c1_df[(c1_df.index>=str(date)+' 09:00:00') & (c1_df.index<=str(date)+' 15:30:00')]
                c2_df = c2_df[(c2_df.index>=str(date)+' 09:00:00') & (c2_df.index<=str(date)+' 15:30:00')]
                p1_df = p1_df[(p1_df.index>=str(date)+' 09:00:00') & (p1_df.index<=str(date)+' 15:30:00')]
                p2_df = p2_df[(p2_df.index>=str(date)+' 09:00:00') & (p2_df.index<=str(date)+' 15:30:00')]
    
                daily_c1_delta, daily_c2_delta, daily_p1_delta, daily_p2_delta = c1_df.delta, c2_df.delta, p1_df.delta, p2_df.delta
                daily_c1_price, daily_c2_price, daily_p1_price, daily_p2_price = c1_df.latest, c2_df.latest, p1_df.latest, p2_df.latest
                daily_c1_spread, daily_c2_spread, daily_p1_spread, daily_p2_spread = c1_df.spread, c2_df.spread, p1_df.spread, p2_df.spread

            timestamps = daily_c1_price.index
   
            c1_delta, c2_delta, p1_delta, p2_delta = daily_c1_delta.iloc[t], daily_c2_delta.iloc[t], daily_p1_delta.iloc[t], daily_p2_delta.iloc[t]
            c1_price, c2_price, p1_price, p2_price = daily_c1_price.iloc[t]*1e4, daily_c2_price.iloc[t]*1e4, daily_p1_price.iloc[t]*1e4, daily_p2_price.iloc[t]*1e4
            c1_spread, c2_spread, p1_spread, p2_spread = daily_c1_spread.iloc[t], daily_c2_spread.iloc[t], daily_p1_spread.iloc[t], daily_p2_spread.iloc[t]

            c1_k = dfoplst[dfoplst.code.str.startswith(c1_con)].exercise_price.iloc[0]
            c2_k = dfoplst[dfoplst.code.str.startswith(c2_con)].exercise_price.iloc[0]
            p1_k = dfoplst[dfoplst.code.str.startswith(p1_con)].exercise_price.iloc[0]
            p2_k = dfoplst[dfoplst.code.str.startswith(p2_con)].exercise_price.iloc[0]

            c1, c2, p1, p2 = get_option_positions(c1_delta, c2_delta, p1_delta, p2_delta, \
                                                  c1_price, c2_price, p1_price, p2_price, \
                                                  all_asset) 
            
            remaining_cash = all_asset - (c1_price*c1 + c2_price*c2 + p1_price*p1 + p2_price*p2)

        c1_delta, c2_delta, p1_delta, p2_delta = daily_c1_delta.iloc[t], daily_c2_delta.iloc[t], daily_p1_delta.iloc[t], daily_p2_delta.iloc[t]
        c1_price, c2_price, p1_price, p2_price = daily_c1_price.iloc[t]*1e4, daily_c2_price.iloc[t]*1e4, daily_p1_price.iloc[t]*1e4, daily_p2_price.iloc[t]*1e4
        c1_spread, c2_spread, p1_spread, p2_spread = daily_c1_spread.iloc[t], daily_c2_spread.iloc[t], daily_p1_spread.iloc[t], daily_p2_spread.iloc[t]
        
        if not rebalance_complete:

            old_c1_price, old_c2_price, old_p1_price, old_p2_price = \
            daily_old_c1_price.iloc[t]*1e4, daily_old_c2_price.iloc[t]*1e4, daily_old_p1_price.iloc[t]*1e4, daily_old_p2_price.iloc[t]*1e4
            
            price_dict = {
                c1_con: c1_price,
                c2_con: c2_price,
                p1_con: p1_price,
                p2_con: p2_price,     
                old_c1_con: old_c1_price,
                old_c2_con: old_c2_price,
                old_p1_con: old_p1_price,
                old_p2_con: old_p2_price, 
            }
            holding_asset = 0
            for c in rebalance_scehdule:
                holding_asset += (rebalance_scehdule[c][rebalance_counter]*price_dict[c])
            all_asset = remaining_cash + holding_asset
            
            rebalance_counter += 1

            if rebalance_counter > 600:
                whether_rebalance = False
                rebalance_complete = True
                c1_con, c2_con, p1_con, p2_con = ed_con_dict['c1'], ed_con_dict['c2'], ed_con_dict['p1'], ed_con_dict['p2']
                c1, c2, p1, p2 = rebalance_scehdule[c1_con][-1], rebalance_scehdule[c2_con][-1], rebalance_scehdule[p1_con][-1], rebalance_scehdule[p2_con][-1]
                rebalance_counter = 1
            else:
                holding_asset = 0
                slippage = 0
                for c in rebalance_scehdule:
                    holding_asset += (rebalance_scehdule[c][rebalance_counter]*price_dict[c])
                    slippage += ( abs(rebalance_scehdule[c][rebalance_counter] - rebalance_scehdule[c][rebalance_counter-1])*8 )
                remaining_cash = all_asset - holding_asset
                
                if contract_month == 0:
                    remaining_cash += slippage
                else:
                    remaining_cash -= slippage
                
        else:

            all_asset = remaining_cash + (c1_price*c1 + c2_price*c2 + p1_price*p1 + p2_price*p2)
 

        curr_perc = daily_perc[t]
        if abs(curr_perc - rebalance_benchmark) > 0.01 and rebalance_complete:
            whether_rebalance = True
            rebalance_benchmark = curr_perc

        whether_cont_trading = c1_spread > 0 and c2_spread > 0 and p1_spread > 0 and p2_spread > 0 and \
                               c1_price > 0 and c2_price > 0 and p1_price > 0 and p2_price > 0
        
        if whether_rebalance and whether_cont_trading and rebalance_complete and t < (14400 - 60*15):

            print(timestamps[t], 'Position Rebalanced!')

            rebalance_complete = False

            st_dict = {
                c1_con: c1,
                c2_con: c2,
                p1_con: p1,
                p2_con: p2,
            }

            price_dict = {
                c1_con: c1_price,
                c2_con: c2_price,
                p1_con: p1_price,
                p2_con: p2_price,     
            }

            old_c1_con = c1_con
            old_c2_con = c2_con
            old_p1_con = p1_con
            old_p2_con = p2_con

            pos_reb.append(t)

            con_rebalance_condition = c1_delta < 0.3 or p1_delta > -0.3
                
            if con_rebalance_condition:
                
                print(timestamps[t], 'Contract Rebalanced!')

                con_reb.append(t)
                c1_con, c2_con, p1_con, p2_con = get_option_contracts(daily_etf_price[t], underlying, date, contract_month, t)

                c1_df = pd.read_pickle('/A/MD/tick/o/processed/'+underlying+'/'+c1_con+'.pickle')
                c2_df = pd.read_pickle('/A/MD/tick/o/processed/'+underlying+'/'+c2_con+'.pickle')
                p1_df = pd.read_pickle('/A/MD/tick/o/processed/'+underlying+'/'+p1_con+'.pickle')
                p2_df = pd.read_pickle('/A/MD/tick/o/processed/'+underlying+'/'+p2_con+'.pickle')
                
                c1_df = c1_df[(c1_df.index>=str(date)+' 09:00:00') & (c1_df.index<=str(date)+' 15:30:00')]
                c2_df = c2_df[(c2_df.index>=str(date)+' 09:00:00') & (c2_df.index<=str(date)+' 15:30:00')]
                p1_df = p1_df[(p1_df.index>=str(date)+' 09:00:00') & (p1_df.index<=str(date)+' 15:30:00')]
                p2_df = p2_df[(p2_df.index>=str(date)+' 09:00:00') & (p2_df.index<=str(date)+' 15:30:00')]
    
                daily_c1_delta, daily_c2_delta, daily_p1_delta, daily_p2_delta = c1_df.delta, c2_df.delta, p1_df.delta, p2_df.delta
                daily_c1_price, daily_c2_price, daily_p1_price, daily_p2_price = c1_df.latest, c2_df.latest, p1_df.latest, p2_df.latest
                daily_c1_spread, daily_c2_spread, daily_p1_spread, daily_p2_spread = c1_df.spread, c2_df.spread, p1_df.spread, p2_df.spread
                    
                c1_delta, c2_delta, p1_delta, p2_delta = daily_c1_delta.iloc[t], daily_c2_delta.iloc[t], daily_p1_delta.iloc[t], daily_p2_delta.iloc[t]
                c1_price, c2_price, p1_price, p2_price = daily_c1_price.iloc[t]*1e4, daily_c2_price.iloc[t]*1e4, daily_p1_price.iloc[t]*1e4, daily_p2_price.iloc[t]*1e4
                c1_spread, c2_spread, p1_spread, p2_spread = daily_c1_spread.iloc[t], daily_c2_spread.iloc[t], daily_p1_spread.iloc[t], daily_p2_spread.iloc[t]

                c1_k = dfoplst[dfoplst.code.str.startswith(c1_con)].exercise_price.iloc[0]
                c2_k = dfoplst[dfoplst.code.str.startswith(c2_con)].exercise_price.iloc[0]
                p1_k = dfoplst[dfoplst.code.str.startswith(p1_con)].exercise_price.iloc[0]
                p2_k = dfoplst[dfoplst.code.str.startswith(p2_con)].exercise_price.iloc[0]

            old_c1_df = pd.read_pickle('/A/MD/tick/o/processed/'+underlying+'/'+old_c1_con+'.pickle')
            old_c2_df = pd.read_pickle('/A/MD/tick/o/processed/'+underlying+'/'+old_c2_con+'.pickle')
            old_p1_df = pd.read_pickle('/A/MD/tick/o/processed/'+underlying+'/'+old_p1_con+'.pickle')
            old_p2_df = pd.read_pickle('/A/MD/tick/o/processed/'+underlying+'/'+old_p2_con+'.pickle')
            
            old_c1_df = old_c1_df[(old_c1_df.index>=str(date)+' 09:00:00') & (old_c1_df.index<=str(date)+' 15:30:00')]
            old_c2_df = old_c2_df[(old_c2_df.index>=str(date)+' 09:00:00') & (old_c2_df.index<=str(date)+' 15:30:00')]
            old_p1_df = old_p1_df[(old_p1_df.index>=str(date)+' 09:00:00') & (old_p1_df.index<=str(date)+' 15:30:00')]
            old_p2_df = old_p2_df[(old_p2_df.index>=str(date)+' 09:00:00') & (old_p2_df.index<=str(date)+' 15:30:00')]
    
            daily_old_c1_price, daily_old_c2_price, daily_old_p1_price, daily_old_p2_price = old_c1_df.latest, old_c2_df.latest, old_p1_df.latest, old_p2_df.latest

            
            c1, c2, p1, p2 = get_option_positions(c1_delta, c2_delta, p1_delta, p2_delta, \
                                                  c1_price, c2_price, p1_price, p2_price, \
                                                  all_asset)  
            ed_dict = {
                c1_con: c1,
                c2_con: c2,
                p1_con: p1,
                p2_con: p2,
            }

            ed_price_dict = {
                c1_con: c1_price,
                c2_con: c2_price,
                p1_con: p1_price,
                p2_con: p2_price,     
            }

            ed_con_dict = {
                'c1': c1_con, 
                'c2': c2_con,
                'p1': p1_con,
                'p2': p2_con,
            }

            price_dict.update(ed_price_dict)

            rebalance_scehdule = get_smooth_adj_schedule(st_dict, ed_dict)

            holding_asset = 0
            slippage = 0
            for c in rebalance_scehdule:
                holding_asset += (rebalance_scehdule[c][rebalance_counter]*price_dict[c])
                slippage += ( abs(rebalance_scehdule[c][rebalance_counter] - rebalance_scehdule[c][rebalance_counter-1])*8 )
            remaining_cash = all_asset - holding_asset
            
            if contract_month == 0:
                remaining_cash += slippage
            else:
                remaining_cash -= slippage


        c1_tv = get_time_value(c1_price, daily_etf_price[t]/1e3, c1_k, 1)
        c2_tv = get_time_value(c2_price, daily_etf_price[t]/1e3, c2_k, 1)
        p1_tv = get_time_value(p1_price, daily_etf_price[t]/1e3, p1_k, 2)
        p2_tv = get_time_value(p2_price, daily_etf_price[t]/1e3, p2_k, 2)
        all_tv = c1_tv*c1 + c2_tv*c2 + p1_tv*p1 + p2_tv*p2

        c1_mb = get_margin(c1_price, daily_etf_price[t]/1e3, c1_k, 1, 'b')
        c2_mb = get_margin(c2_price, daily_etf_price[t]/1e3, c2_k, 1, 'b')
        p1_mb = get_margin(p1_price, daily_etf_price[t]/1e3, p1_k, 2, 'b')
        p2_mb = get_margin(p2_price, daily_etf_price[t]/1e3, p2_k, 2, 'b')
        all_mb = c1_mb*c1 + c2_mb*c2 + p1_mb*p1 + p2_mb*p2

        c1_ms = get_margin(c1_price, daily_etf_price[t]/1e3, c1_k, 1, 's')
        c2_ms = get_margin(c2_price, daily_etf_price[t]/1e3, c2_k, 1, 's')
        p1_ms = get_margin(p1_price, daily_etf_price[t]/1e3, p1_k, 2, 's')
        p2_ms = get_margin(p2_price, daily_etf_price[t]/1e3, p2_k, 2, 's')
        all_ms = c1_ms*c1 + c2_ms*c2 + p1_ms*p1 + p2_ms*p2
       
        signal.append(all_asset)
        tv.append(all_tv)
        mb.append(all_mb)
        ms.append(all_ms)


    print(timestamps[-1], c1_con, c1, c2_con, c2, p1_con, p1, p2_con, p2, ' | Cash:', remaining_cash, ' | Rebalance: (pos|con)', str(len(pos_reb))+' | '+str(len(con_reb)))
    
    con_reb = np.array(con_reb)
    pos_reb = np.array(pos_reb)
    
    return signal, tv, mb, ms, c1_con, c2_con, p1_con, p2_con, c1, c2, p1, p2, remaining_cash, timestamps.tolist(), con_reb, pos_reb



def prep_data(underlying, contract_month, st_dt, ed_dt):

    all_t_days = pickle_read_tdays()[:-1]
    today = datetime.datetime.now().date()

    # st_dt, ed_dt = datetime.datetime.strptime(st_dt, '%Y-%m-%d').date(), datetime.datetime.strptime(ed_dt, '%Y-%m-%d').date()
    days = all_t_days[
        (all_t_days >= st_dt) & \
        (all_t_days <= min(ed_dt, datetime.datetime.strptime(str(all_etf50_price.index[-1]).split()[0], '%Y-%m-%d').date()))
    ]
    
    # full_info = pd.read_pickle(f'{MDLSTDIR}/all_securities/full.pickle')
    all_futures_ex_dates = sorted(full_info[(full_info.type == 'futures') & (full_info.name.str[:2]=='IH')].end_date.dt.strftime("%Y-%m-%d").unique())[:-1]
    all_options_ex_dates = sorted(full_info[(full_info.type == 'options') & (full_info.name.str[:2] == '10')].end_date.dt.strftime("%Y-%m-%d").unique())
    all_options_ex_dates = np.array([datetime.datetime.strptime(date, "%Y-%m-%d").date() for date in all_options_ex_dates])
    all_futures_ex_dates = np.array([datetime.datetime.strptime(date, "%Y-%m-%d").date() for date in all_futures_ex_dates])
        
    all_asset = 10000

    vop_dict = {'jz':{}, 'tv':{}, 'margin':{}, 'rebalance':{}}
    
    signals = []
    daily_c1_delta, daily_c2_delta, daily_p1_delta, daily_p2_delta = None, None, None, None
    daily_c1_price, daily_c2_price, daily_p1_price, daily_p2_price = None, None, None, None
    daily_old_c1_price, daily_old_c2_price, daily_old_p1_price, daily_old_p2_price = None, None, None, None
    daily_c1_spread, daily_c2_spread, daily_p1_spread, daily_p2_spread = None, None, None, None
    c1_con, c2_con, p1_con, p2_con = None, None, None, None
    old_c1_con, old_c2_con, old_p1_con, old_p2_con = None, None, None, None
    c1, c2, p1, p2 = None, None, None, None
    c1_k, c2_k, p1_k, p2_k = None, None, None, None
    remaining_cash = None
    
    for date in tqdm(days[:]):

        if underlying == '510050':
            daily_etf_price = all_etf50_price.loc[str(date)+' 09:00:00':str(date)+' 15:30:00'].current.values * 1000
        elif underlying == '510300':
            daily_etf_price = all_etf300_price.loc[str(date)+' 09:00:00':str(date)+' 15:30:00'].current.values * 1000
        elif underlying == '510500':
            daily_etf_price = all_etf500_price.loc[str(date)+' 09:00:00':str(date)+' 15:30:00'].current.values * 1000
        elif underlying == '159915':
            daily_etf_price = all_etf915_price.loc[str(date)+' 09:00:00':str(date)+' 15:30:00'].current.values * 1000
        elif underlying == '588000':
            daily_etf_price = all_etfk50_price.loc[str(date)+' 09:00:00':str(date)+' 15:30:00'].current.values * 1000

        daily_etf50_price = all_etf50_price.loc[str(date)+' 09:00:00':str(date)+' 15:30:00'].current.values * 1000
        daily_etf300_price = all_etf300_price.loc[str(date)+' 09:00:00':str(date)+' 15:30:00'].current.values * 1000
        daily_etf500_price = all_etf500_price.loc[str(date)+' 09:00:00':str(date)+' 15:30:00'].current.values * 1000
        daily_etf915_price = all_etf915_price.loc[str(date)+' 09:00:00':str(date)+' 15:30:00'].current.values * 1000
        daily_etfk50_price = all_etfk50_price.loc[str(date)+' 09:00:00':str(date)+' 15:30:00'].current.values * 1000

        etf50_prev_close = d_etf50_price.loc[str(date)].pre_close * 1000 
        etf300_prev_close = d_etf300_price.loc[str(date)].pre_close * 1000 
        etf500_prev_close = d_etf500_price.loc[str(date)].pre_close * 1000 
        etf915_prev_close = d_etf915_price.loc[str(date)].pre_close * 1000 
        etfk50_prev_close = d_etfk50_price.loc[str(date)].pre_close * 1000 

        daily_etf50_perc = daily_etf50_price / etf50_prev_close - 1
        daily_etf300_perc = daily_etf300_price / etf300_prev_close - 1
        daily_etf500_perc = daily_etf500_price / etf500_prev_close - 1
        daily_etf915_perc = daily_etf915_price / etf915_prev_close - 1
        daily_etfk50_perc = daily_etfk50_price / etfk50_prev_close - 1

        daily_perc = np.mean((daily_etf50_perc, daily_etf300_perc, daily_etf500_perc, daily_etf915_perc, daily_etfk50_perc), axis=0)
    
        curr_month = get_month(date)

        if curr_month not in vop_dict['jz']:
            vop_dict['jz'][curr_month] = []
            vop_dict['tv'][curr_month] = []
            vop_dict['margin'][curr_month] = {'b':[], 's':[]}

            vop_dict['rebalance'][curr_month] = {'con':[], 'pos':[], 'daily_count':[]}

        if c1_con is not None:
            
            c1_df = pd.read_pickle('/A/MD/tick/o/processed/'+underlying+'/'+c1_con+'.pickle')
            c2_df = pd.read_pickle('/A/MD/tick/o/processed/'+underlying+'/'+c2_con+'.pickle')
            p1_df = pd.read_pickle('/A/MD/tick/o/processed/'+underlying+'/'+p1_con+'.pickle')
            p2_df = pd.read_pickle('/A/MD/tick/o/processed/'+underlying+'/'+p2_con+'.pickle')
            
            c1_df = c1_df[(c1_df.index>=str(date)+' 09:00:00') & (c1_df.index<=str(date)+' 15:30:00')]
            c2_df = c2_df[(c2_df.index>=str(date)+' 09:00:00') & (c2_df.index<=str(date)+' 15:30:00')]
            p1_df = p1_df[(p1_df.index>=str(date)+' 09:00:00') & (p1_df.index<=str(date)+' 15:30:00')]
            p2_df = p2_df[(p2_df.index>=str(date)+' 09:00:00') & (p2_df.index<=str(date)+' 15:30:00')]

            daily_c1_delta, daily_c2_delta, daily_p1_delta, daily_p2_delta = c1_df.delta, c2_df.delta, p1_df.delta, p2_df.delta
            daily_c1_price, daily_c2_price, daily_p1_price, daily_p2_price = c1_df.latest, c2_df.latest, p1_df.latest, p2_df.latest
            daily_c1_spread, daily_c2_spread, daily_p1_spread, daily_p2_spread = c1_df.spread, c2_df.spread, p1_df.spread, p2_df.spread

            c1_k = dfoplst[dfoplst.code.str.startswith(c1_con)].exercise_price.iloc[0]
            c2_k = dfoplst[dfoplst.code.str.startswith(c2_con)].exercise_price.iloc[0]
            p1_k = dfoplst[dfoplst.code.str.startswith(p1_con)].exercise_price.iloc[0]
            p2_k = dfoplst[dfoplst.code.str.startswith(p2_con)].exercise_price.iloc[0]
        
        next_ex_date = all_options_ex_dates[all_options_ex_dates >= date][0]
        remaining_time = (next_ex_date - date).total_seconds() / (60*60*24)
        
        signal, tv, mb, ms, c1_con, c2_con, p1_con, p2_con, c1, c2, p1, p2, \
        remaining_cash, daily_timestamps, con_reb, pos_reb \
            = compute_daily_signal(
               daily_c1_delta, daily_c2_delta, daily_p1_delta, daily_p2_delta, \
               daily_c1_price, daily_c2_price, daily_p1_price, daily_p2_price, \
               daily_c1_spread, daily_c2_spread, daily_p1_spread, daily_p2_spread, \
               daily_etf_price, \
               c1_con, c2_con, p1_con, p2_con, \
               c1, c2, p1, p2, \
               c1_k, c2_k, p1_k, p2_k, \
               remaining_cash, \
               contract_month, \
               daily_perc, \
               date, underlying, all_asset)

        all_asset = signal[-1]
    
        vop_df = pd.DataFrame(np.array(signal)*0.01, index=daily_timestamps)  
        tv_df = pd.DataFrame(np.array(tv), index=daily_timestamps)  
        mb_df = pd.DataFrame(np.array(mb), index=daily_timestamps)  
        ms_df = pd.DataFrame(np.array(ms), index=daily_timestamps)  

        vop_dict['jz'][curr_month].append(vop_df)
        vop_dict['tv'][curr_month].append(tv_df)
        vop_dict['margin'][curr_month]['b'].append(mb_df)
        vop_dict['margin'][curr_month]['s'].append(ms_df)

        vop_dict['rebalance'][curr_month]['con'].append(con_reb)
        vop_dict['rebalance'][curr_month]['pos'].append(pos_reb)
        vop_dict['rebalance'][curr_month]['daily_count'].append(len(pos_reb))
       
        if date in all_options_ex_dates:
            all_asset = 10000
            c1_con, c2_con, p1_con, p2_con = None, None, None, None

            vop_dict['jz'][curr_month] = pd.concat(vop_dict['jz'][curr_month])
            vop_dict['tv'][curr_month] = pd.concat(vop_dict['tv'][curr_month])
            vop_dict['margin'][curr_month]['b'] = pd.concat(vop_dict['margin'][curr_month]['b'])
            vop_dict['margin'][curr_month]['s'] = pd.concat(vop_dict['margin'][curr_month]['s'])

            vop_dict['rebalance'][curr_month]['con'] = np.concatenate(vop_dict['rebalance'][curr_month]['con'])
            vop_dict['rebalance'][curr_month]['pos'] = np.concatenate(vop_dict['rebalance'][curr_month]['pos'])

    if type(vop_dict['jz'][curr_month]) == list:
        vop_dict['jz'][curr_month] = pd.concat(vop_dict['jz'][curr_month])
    if type(vop_dict['tv'][curr_month]) == list:
        vop_dict['tv'][curr_month] = pd.concat(vop_dict['tv'][curr_month])
    if type(vop_dict['margin'][curr_month]['b']) == list:
        vop_dict['margin'][curr_month]['b'] = pd.concat(vop_dict['margin'][curr_month]['b'])
    if type(vop_dict['margin'][curr_month]['s']) == list:
        vop_dict['margin'][curr_month]['s'] = pd.concat(vop_dict['margin'][curr_month]['s'])
    if type(vop_dict['rebalance'][curr_month]['con']) == list:
        vop_dict['rebalance'][curr_month]['con'] = np.concatenate(vop_dict['rebalance'][curr_month]['con'])
    if type(vop_dict['rebalance'][curr_month]['pos']) == list:
        vop_dict['rebalance'][curr_month]['pos'] = np.concatenate(vop_dict['rebalance'][curr_month]['pos'])

    manifest = {
        c1_con: c1,
        c2_con: c2,
        p1_con: p1,
        p2_con: p2
    }

    # return vop_dict, manifest, remaining_cash
    return vop_dict



def get_hvop(vop, nvop):
    
    hvop = {'jz':{} ,'tv':{}, 'margin':{}}
    for mon in vop['jz'].keys():
        hvop['jz'][mon] = vop['jz'][mon] - nvop['jz'][mon]
        hvop['tv'][mon] = vop['tv'][mon]
        hvop['margin'][mon] = vop['margin'][mon]['s'] + nvop['margin'][mon]['b']
        
    return hvop


def get_hvop_index(hvops):

    hvop_index = {'jz':{}, 'tv':{}, 'margin':{}, 'abs':{}, 'b':{}}
    mons = hvops[0]['jz'].keys() 
    for mon in mons:
        avg_jz = []
        avg_tv = []
        avg_margin = []
        for hvop in hvops:
            avg_jz.append(hvop['jz'][mon])
            avg_tv.append(hvop['tv'][mon])
            avg_margin.append(hvop['margin'][mon])
        hvop_index['jz'][mon] = pd.concat(avg_jz, axis=1).sum(axis=1)
        hvop_index['tv'][mon] = pd.concat(avg_tv, axis=1).sum(axis=1)
        hvop_index['margin'][mon] = pd.concat(avg_margin, axis=1).sum(axis=1)

        st_dt, ed_dt = hvops[0]['jz'][mon].index[0], hvops[0]['jz'][mon].index[-1]

        b50_mon = b50.loc[st_dt:ed_dt]
        b300_mon = b300.loc[st_dt:ed_dt]
        b500_mon = b500.loc[st_dt:ed_dt]
        b915_mon = b915.loc[st_dt:ed_dt]
        bk50_mon = bk50.loc[st_dt:ed_dt]

        hvop_index['abs'][mon] = hvop_index['jz'][mon]
        hvop_index['b'][mon] = (b50_mon/b50_mon.iloc[0] + b300_mon/b300_mon.iloc[0] + b500_mon/b500_mon.iloc[0] + b915_mon/b915_mon.iloc[0] + bk50_mon/bk50_mon.iloc[0])/5
       
    return hvop_index



def get_vop_index(hvops):

    hvop_index = {'jz':{}, 'tv':{}, 'margin':{}, 'abs':{}, 'b':{}}
    mons = hvops[0]['jz'].keys() 
    for mon in mons:
        avg_jz = []
        avg_tv = []
        avg_margin_b = []
        avg_margin_s = []
        for hvop in hvops:
            avg_jz.append(hvop['jz'][mon])
            avg_tv.append(hvop['tv'][mon])
            avg_margin_b.append(hvop['margin'][mon]['b'])
            avg_margin_s.append(hvop['margin'][mon]['s'])
        hvop_index['jz'][mon] = pd.concat(avg_jz, axis=1).sum(axis=1)
        hvop_index['tv'][mon] = pd.concat(avg_tv, axis=1).sum(axis=1)
        hvop_index['margin'][mon] = {'b': pd.concat(avg_margin_b, axis=1).sum(axis=1), 's': pd.concat(avg_margin_s, axis=1).sum(axis=1)}

        st_dt, ed_dt = hvops[0]['jz'][mon].index[0], hvops[0]['jz'][mon].index[-1]

        b50_mon = b50.loc[st_dt:ed_dt]
        b300_mon = b300.loc[st_dt:ed_dt]
        b500_mon = b500.loc[st_dt:ed_dt]
        b915_mon = b915.loc[st_dt:ed_dt]
        bk50_mon = bk50.loc[st_dt:ed_dt]

        hvop_index['abs'][mon] = hvop_index['jz'][mon] - len(hvops)*100
        hvop_index['b'][mon] = (b50_mon/b50_mon.iloc[0] + b300_mon/b300_mon.iloc[0] + b500_mon/b500_mon.iloc[0] + b915_mon/b915_mon.iloc[0] + bk50_mon/bk50_mon.iloc[0])/5
       
    return hvop_index




def get_cross_perf(hvop):
    
    cross_mon = {'jz':[], 'tv':[], 'margin':[]}
    for mon in hvop['jz'].keys():
        cross_mon['jz'].append( np.concatenate((np.zeros(360000-hvop['jz'][mon].values.flatten().shape[0]), hvop['jz'][mon].values.flatten())).reshape(-1,1) )
        cross_mon['tv'].append( np.concatenate((np.zeros(360000-hvop['tv'][mon].values.flatten().shape[0]), hvop['tv'][mon].values.flatten())).reshape(-1,1)  )
        cross_mon['margin'].append( np.concatenate((np.zeros(360000-hvop['margin'][mon].values.flatten().shape[0]), hvop['margin'][mon].values.flatten())).reshape(-1,1)  )
    cross_mon['jz'] = np.concatenate(cross_mon['jz'], axis=1).mean(axis=1)
    cross_mon['tv'] = np.concatenate(cross_mon['tv'], axis=1).mean(axis=1)
    cross_mon['margin'] = np.concatenate(cross_mon['margin'], axis=1).mean(axis=1)
    
    return cross_mon



contract_month_dict = {
    0: 'VOP',
    1: 'NVOP'
}

date_dict = {
    '510050': datetime.date(2020,1,2), 
    '510300': datetime.date(2020,1,2), 
    '159919': datetime.date(2020,1,2), 
    '510500': datetime.date(2022,10,1), 
    '159922': datetime.date(2022,10,1), 
    '159915': datetime.date(2022,10,1), 
    '588000': datetime.date(2023,6,5), 
    '588080': datetime.date(2023,6,5), 
}

dfoplst = pickle_read_oplst()
full_info = pd.read_pickle('/A/MD/LST/all_securities/full.pickle')
today = datetime.datetime.now().date()

all_op_exp_dts = get_all_op_ex_dates()
all_t_days = pickle_read_tdays()
last_op_exp_dt = np.sort(all_op_exp_dts[all_op_exp_dts < datetime.date.today()])[-1]
last_op_exp_dt_idx = np.argwhere(all_t_days == last_op_exp_dt)[0][0]
month_first_dt = all_t_days[last_op_exp_dt_idx+1]

today = datetime.date.today()

all_etf50_price = pd.read_pickle('/A/MD/tick/s/processed/510050.pickle')
all_etf300_price = pd.read_pickle('/A/MD/tick/s/processed/510300.pickle')
all_etf500_price = pd.read_pickle('/A/MD/tick/s/processed/510500.pickle')
all_etf915_price = pd.read_pickle('/A/MD/tick/s/processed/159915.pickle')
all_etfk50_price = pd.read_pickle('/A/MD/tick/s/processed/588000.pickle')

d_etf50_price = pd.read_pickle('/A/MD/1d/s/md_s_510050.XSHG.pickle')
d_etf300_price = pd.read_pickle('/A/MD/1d/s/md_s_510300.XSHG.pickle')
d_etf500_price = pd.read_pickle('/A/MD/1d/s/md_s_510500.XSHG.pickle')
d_etf915_price = pd.read_pickle('/A/MD/1d/s/md_s_159915.XSHE.pickle')
d_etfk50_price = pd.read_pickle('/A/MD/1d/s/md_s_588000.XSHG.pickle')

b50 = all_etf50_price.current
b300 = all_etf300_price.current
b500 = all_etf500_price.current
b915 = all_etf915_price.current
bk50 = all_etfk50_price.current


vop_50 = prep_data('510050', 0, month_first_dt, today)
nvop_50 = prep_data('510050', 1, month_first_dt, today)

vop_300 = prep_data('510300', 0, month_first_dt, today)
nvop_300 = prep_data('510300', 1, month_first_dt, today)

vop_500 = prep_data('510500', 0, month_first_dt, today)
nvop_500 = prep_data('510500', 1, month_first_dt, today)

vop_915 = prep_data('159915', 0, month_first_dt, today)
nvop_915 = prep_data('159915', 1, month_first_dt, today)

vop_k50 = prep_data('588000', 0, month_first_dt, today)
nvop_k50 = prep_data('588000', 1, month_first_dt, today)


######################################################


with open('/A/MD/A_history/vop_50.pkl', 'rb') as file:
    new_vop_50 = pickle.load(file)

for mon in vop_50['jz'].keys():
    new_vop_50['jz'][mon] = vop_50['jz'][mon]
    new_vop_50['tv'][mon] = vop_50['tv'][mon]
    new_vop_50['margin'][mon] = {}
    new_vop_50['margin'][mon]['b'] = vop_50['margin'][mon]['b']
    new_vop_50['margin'][mon]['s'] = vop_50['margin'][mon]['s']

with open('/A/MD/A_history/vop_50.pkl', 'wb') as file:
    pickle.dump(new_vop_50, file)


with open('/A/MD/A_history/nvop_50.pkl', 'rb') as file:
    new_nvop_50 = pickle.load(file)

for mon in nvop_50['jz'].keys():
    new_nvop_50['jz'][mon] = vop_50['jz'][mon]
    new_nvop_50['tv'][mon] = vop_50['tv'][mon]
    new_nvop_50['margin'][mon] = {}
    new_nvop_50['margin'][mon]['b'] = vop_50['margin'][mon]['b']
    new_nvop_50['margin'][mon]['s'] = vop_50['margin'][mon]['s']

with open('/A/MD/A_history/nvop_50.pkl', 'wb') as file:
    pickle.dump(new_nvop_50, file)

######################################################

with open('/A/MD/A_history/vop_300.pkl', 'rb') as file:
    new_vop_300 = pickle.load(file)

for mon in vop_300['jz'].keys():
    new_vop_300['jz'][mon] = vop_300['jz'][mon]
    new_vop_300['tv'][mon] = vop_300['tv'][mon]
    new_vop_300['margin'][mon] = {}
    new_vop_300['margin'][mon]['b'] = vop_300['margin'][mon]['b']
    new_vop_300['margin'][mon]['s'] = vop_300['margin'][mon]['s']

with open('/A/MD/A_history/vop_300.pkl', 'wb') as file:
    pickle.dump(new_vop_300, file)


with open('/A/MD/A_history/nvop_300.pkl', 'rb') as file:
    new_nvop_300 = pickle.load(file)

for mon in nvop_300['jz'].keys():
    new_nvop_300['jz'][mon] = nvop_300['jz'][mon]
    new_nvop_300['tv'][mon] = nvop_300['tv'][mon]
    new_nvop_300['margin'][mon] = {}
    new_nvop_300['margin'][mon]['b'] = nvop_300['margin'][mon]['b']
    new_nvop_300['margin'][mon]['s'] = nvop_300['margin'][mon]['s']

with open('/A/MD/A_history/nvop_300.pkl', 'wb') as file:
    pickle.dump(new_nvop_300, file)


######################################################

with open('/A/MD/A_history/vop_500.pkl', 'rb') as file:
    new_vop_500 = pickle.load(file)

for mon in vop_500['jz'].keys():
    new_vop_500['jz'][mon] = vop_500['jz'][mon]
    new_vop_500['tv'][mon] = vop_500['tv'][mon]
    new_vop_500['margin'][mon] = {}
    new_vop_500['margin'][mon]['b'] = vop_500['margin'][mon]['b']
    new_vop_500['margin'][mon]['s'] = vop_500['margin'][mon]['s']

with open('/A/MD/A_history/vop_500.pkl', 'wb') as file:
    pickle.dump(new_vop_500, file)

with open('/A/MD/A_history/nvop_500.pkl', 'rb') as file:
    new_nvop_500 = pickle.load(file)

for mon in nvop_500['jz'].keys():
    new_nvop_500['jz'][mon] = nvop_500['jz'][mon]
    new_nvop_500['tv'][mon] = nvop_500['tv'][mon]
    new_nvop_500['margin'][mon] = {}
    new_nvop_500['margin'][mon]['b'] = nvop_500['margin'][mon]['b']
    new_nvop_500['margin'][mon]['s'] = nvop_500['margin'][mon]['s']

with open('/A/MD/A_history/nvop_500.pkl', 'wb') as file:
    pickle.dump(new_nvop_500, file)



######################################################

with open('/A/MD/A_history/vop_915.pkl', 'rb') as file:
    new_vop_915 = pickle.load(file)

for mon in vop_915['jz'].keys():
    new_vop_915['jz'][mon] = vop_915['jz'][mon]
    new_vop_915['tv'][mon] = vop_915['tv'][mon]
    new_vop_915['margin'][mon] = {}
    new_vop_915['margin'][mon]['b'] = vop_915['margin'][mon]['b']
    new_vop_915['margin'][mon]['s'] = vop_915['margin'][mon]['s']

with open('/A/MD/A_history/vop_915.pkl', 'wb') as file:
    pickle.dump(new_vop_915, file)

with open('/A/MD/A_history/nvop_915.pkl', 'rb') as file:
    new_nvop_915 = pickle.load(file)

for mon in nvop_915['jz'].keys():
    new_nvop_915['jz'][mon] = nvop_915['jz'][mon]
    new_nvop_915['tv'][mon] = nvop_915['tv'][mon]
    new_nvop_915['margin'][mon] = {}
    new_nvop_915['margin'][mon]['b'] = nvop_915['margin'][mon]['b']
    new_nvop_915['margin'][mon]['s'] = nvop_915['margin'][mon]['s']

with open('/A/MD/A_history/nvop_915.pkl', 'wb') as file:
    pickle.dump(new_nvop_915, file)

######################################################

with open('/A/MD/A_history/vop_k50.pkl', 'rb') as file:
    new_vop_k50 = pickle.load(file)

for mon in vop_k50['jz'].keys():
    new_vop_k50['jz'][mon] = vop_k50['jz'][mon]
    new_vop_k50['tv'][mon] = vop_k50['tv'][mon]
    new_vop_k50['margin'][mon] = {}
    new_vop_k50['margin'][mon]['b'] = vop_k50['margin'][mon]['b']
    new_vop_k50['margin'][mon]['s'] = vop_k50['margin'][mon]['s']

with open('/A/MD/A_history/vop_k50.pkl', 'wb') as file:
    pickle.dump(new_vop_k50, file)

with open('/A/MD/A_history/nvop_k50.pkl', 'rb') as file:
    new_nvop_k50 = pickle.load(file)

for mon in nvop_k50['jz'].keys():
    new_nvop_k50['jz'][mon] = nvop_k50['jz'][mon]
    new_nvop_k50['tv'][mon] = nvop_k50['tv'][mon]
    new_nvop_k50['margin'][mon] = {}
    new_nvop_k50['margin'][mon]['b'] = nvop_k50['margin'][mon]['b']
    new_nvop_k50['margin'][mon]['s'] = nvop_k50['margin'][mon]['s']

with open('/A/MD/A_history/nvop_k50.pkl', 'wb') as file:
    pickle.dump(new_nvop_k50, file)

######################################################


with open('/A/MD/A_history/vop_50.pkl', 'rb') as file: 
    vop_50 = pickle.load(file)

with open('/A/MD/A_history/nvop_50.pkl', 'rb') as file: 
    nvop_50 = pickle.load(file)

with open('/A/MD/A_history/vop_300.pkl', 'rb') as file: 
    vop_300 = pickle.load(file)

with open('/A/MD/A_history/nvop_300.pkl', 'rb') as file: 
    nvop_300 = pickle.load(file)

with open('/A/MD/A_history/vop_500.pkl', 'rb') as file: 
    vop_500 = pickle.load(file)

with open('/A/MD/A_history/nvop_500.pkl', 'rb') as file: 
    nvop_500 = pickle.load(file)

with open('/A/MD/A_history/vop_915.pkl', 'rb') as file: 
    vop_915 = pickle.load(file)

with open('/A/MD/A_history/nvop_915.pkl', 'rb') as file: 
    nvop_915 = pickle.load(file)

with open('/A/MD/A_history/vop_k50.pkl', 'rb') as file: 
    vop_k50 = pickle.load(file)

with open('/A/MD/A_history/nvop_k50.pkl', 'rb') as file: 
    nvop_k50 = pickle.load(file)


hvop_50 = get_hvop(vop_50, nvop_50)
hvop_300 = get_hvop(vop_300, nvop_300)
hvop_500 = get_hvop(vop_500, nvop_500)
hvop_915 = get_hvop(vop_915, nvop_915)
hvop_k50 = get_hvop(vop_k50, nvop_k50)

hvop_index = get_hvop_index((hvop_50, hvop_300, hvop_500, hvop_915, hvop_k50))

vop_index = get_vop_index((vop_50, vop_300, vop_500, vop_915, vop_k50))

with open('/A/MD/A_history/hvop_index.pkl', 'wb') as file:
    pickle.dump(hvop_index, file)

with open('/A/MD/A_history/vop_index.pkl', 'wb') as file:
    pickle.dump(vop_index, file)

print('COMPLETE!')