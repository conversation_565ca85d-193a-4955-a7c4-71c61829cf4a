from nb_common import *
import copy
from tqdm.notebook import tqdm
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
import talib as ta
from ut_redis import FrRedis
import pickle

rd170 = FrRedis("168.36.1.170")
rd170 = FrRedis("168.36.1.170")

navs = pd.read_excel('/S/MD/NAV/nav_from_exchange.xlsx').astype(str).set_index('icode')

def get_all_op_ex_dates():
    full_info = pd.read_pickle(f'{MDLSTDIR}/all_securities/full.pickle')
    all_options_ex_dates = sorted(
        full_info[(full_info.type == 'options') & (full_info.name.str[:2] == '10')].end_date.dt.strftime(
            "%Y-%m-%d").unique())
    all_options_ex_dates = np.array(
        [datetime.datetime.strptime(date, "%Y-%m-%d").date() for date in all_options_ex_dates])

    return all_options_ex_dates


def just_pass_opexdt(forced_update=False):
    all_t_days = pickle_read_tdays()
    last_t_dt = np.sort(all_t_days[all_t_days < datetime.date.today()])[-1]
    if forced_update:
        return True
    else:
        return last_t_dt in get_all_op_ex_dates()


def get_e_ratio(etf_kind):
    
    underlying_dict = {
        '510050': '510050',
        '510100': '510050',
        '510300': '510300',
        '510310': '510300',
        '510330': '510300',
        '159919': '510300',
        '510500': '510500',
        '159922': '510500',
        '159915': '159915',
        '159952': '159915',
        '588000': '588000',
        '588080': '588000',
    }

    etf_file_type = 0 if type(navs.loc[etf_kind]) == pd.core.frame.DataFrame else 1
    base_file_type = 0 if type(navs.loc[underlying_dict[etf_kind]]) == pd.core.frame.DataFrame else 1
    
    etf = float(navs.loc[etf_kind].sort_values(by=['time']).nav.iloc[-1]) if etf_file_type == 0 else float(navs.loc[etf_kind].nav)
    base = float(navs.loc[underlying_dict[etf_kind]].sort_values(by=['time']).nav.iloc[-1]) if base_file_type == 0 else float(navs.loc[underlying_dict[etf_kind]].nav)
    ratio = base / etf

    etf_time = navs.loc[etf_kind].sort_values(by=['time']).time.iloc[-1] if etf_file_type == 0 else navs.loc[etf_kind].time
    base_time = navs.loc[underlying_dict[etf_kind]].sort_values(by=['time']).time.iloc[-1] if base_file_type == 0 else navs.loc[underlying_dict[etf_kind]].time
    if etf_time != base_time:
        raise ValueError('ETF Time Unmatched!')
    return ratio


def get_f_ratio(etf_kind, whether_round=True):

    underlying_dict = {
        '510050': ('IH', '000016', '50ETF', 30),
        '510100': ('IH', '000016', '50ETF', 30),
        '510300': ('IF', '000300', '300ETF', 30),
        '510310': ('IF', '000300', '310ETF', 30),
        '510330': ('IF', '000300', '330ETF', 30),
        '159919': ('IF', '000300', '919ETF', 30),
        '510500': ('IC', '000905', '500ETF', 20),
        '159922': ('IC', '000905', '922ETF', 20),
        '512100': ('IM', '000852', '1000ETF', 20),
    }


    etf_file_type = 0 if type(navs.loc[etf_kind]) == pd.core.frame.DataFrame else 1
    
    date = navs.loc[etf_kind].sort_values(by=['time']).time.iloc[-1] if etf_file_type == 0 else navs.loc[etf_kind].time
    index_price = pickle_read_md1d_i(underlying_dict[etf_kind][1]).loc[str(date)].close
    etf_price = float(navs.loc[etf_kind].sort_values(by=['time']).nav.iloc[-1])*1e3 if etf_file_type == 0 else float(navs.loc[etf_kind].nav)*1e3
    ratio = index_price / etf_price
    amt_ext = ratio * underlying_dict[etf_kind][3]
    amt_dec = amt_ext - int(amt_ext)
    dec_choices = np.array([0., 0.2, 0.25, 0.33, 0.5, 0.67, 0.75, 0.8, 1.])
    dec_choice = dec_choices[np.argmin(np.abs(amt_dec - dec_choices))]
    amt = (int(amt_ext) + dec_choice) if whether_round else amt_ext
    zero_multiplier = amt / underlying_dict[etf_kind][3] if whether_round else amt_ext / underlying_dict[etf_kind][3]
    
    return amt, zero_multiplier
    

if just_pass_opexdt(forced_update=False):

    underlying_dict = {
        '510050': '510050',
        '510100': '510050',
        '510300': '510300',
        '510310': '510300',
        '510330': '510300',
        '159919': '510300',
        '510500': '510500',
        '159922': '510500',
        '159915': '159915',
        '159952': '159915',
        '588000': '588000',
        '588080': '588000',
    }
    
    ratio = {}
    for und in underlying_dict.keys():
        ratio[und] = get_e_ratio(und)

    indices = list(ratio.keys())
    cols = ['510050','510300','159919','510500','159922','588000','588080']
    e_ovol = pd.DataFrame(index=indices, columns=cols) # Index: ETF, Column: OP
    
    e_ovol.loc['510050','510050'] = 10000
    e_ovol.loc['510300','510300'] = 10000
    e_ovol.loc['159919','159919'] = 10000
    e_ovol.loc['510500','510500'] = 10000
    e_ovol.loc['159922','159922'] = 10000
    e_ovol.loc['159915','159915'] = 10000
    e_ovol.loc['588000','588000'] = 10000
    e_ovol.loc['588080','588080'] = 10000

    e_ovol.loc['510100','510050'] = int(round(ratio['510100']/ratio['510050'],2)*1e4+1e-6)
    
    e_ovol.loc['510300','159919'] = int(round(ratio['510300']/ratio['159919'],2)*1e4+1e-6)
    e_ovol.loc['510310','159919'] = int(round(ratio['510310']/ratio['159919'],2)*1e4+1e-6)
    e_ovol.loc['510330','159919'] = int(round(ratio['510330']/ratio['159919'],2)*1e4+1e-6)
    
    e_ovol.loc['159919','510300'] = int(round(ratio['159919']/ratio['510300'],2)*1e4+1e-6)
    e_ovol.loc['510310','510300'] = int(round(ratio['510310']/ratio['510300'],2)*1e4+1e-6)
    e_ovol.loc['510330','510300'] = int(round(ratio['510330']/ratio['510300'],2)*1e4+1e-6)
    
    e_ovol.loc['510500','159922'] = int(round(ratio['510500']/ratio['159922'],2)*1e4+1e-6)
    e_ovol.loc['159922','510500'] = int(round(ratio['159922']/ratio['510500'],2)*1e4+1e-6)
    
    e_ovol.loc['159952','159915'] = int(round(ratio['159952']/ratio['159915'],2)*1e4+1e-6)
    
    e_ovol.loc['588080','588000'] = int(round(ratio['588080']/ratio['588000'],2)*1e4+1e-6)
    e_ovol.loc['588000','588080'] = int(round(ratio['588000']/ratio['588080'],2)*1e4+1e-6)

    e_ovol.to_csv('/A/pk_tracker/e_ovol.csv')

    print('E_ovol Table Update Complete!')

else:

    print('No Need to Update E_ovol!')



if just_pass_opexdt(forced_update=False):

    underlying_dict = {
        '510050': ('IH', '000016', '50ETF', 30),
        '510100': ('IH', '000016', '50ETF', 30),
        '510300': ('IF', '000300', '300ETF', 30),
        '510310': ('IF', '000300', '310ETF', 30),
        '510330': ('IF', '000300', '330ETF', 30),
        '159919': ('IF', '000300', '919ETF', 30),
        '510500': ('IC', '000905', '500ETF', 20),
        '159922': ('IC', '000905', '922ETF', 20),
        '512100': ('IM', '000852', '1000ETF', 20),
    }

    f_ovol = pd.DataFrame(index=['IH','IF','IC','IM'], columns=list(underlying_dict.keys())) # Index: F, Columns: E/OP
    
    f_ovol.loc['IH', '510050'] = get_f_ratio('510050')[0]
    f_ovol.loc['IH', '510100'] = get_f_ratio('510100')[0]
    
    f_ovol.loc['IF', '510300'] = get_f_ratio('510300')[0]
    f_ovol.loc['IF', '510310'] = get_f_ratio('510310')[0]
    f_ovol.loc['IF', '510330'] = get_f_ratio('510330')[0]
    f_ovol.loc['IF', '159919'] = get_f_ratio('159919')[0]
    
    f_ovol.loc['IC', '510500'] = get_f_ratio('510500')[0]
    f_ovol.loc['IC', '159922'] = get_f_ratio('159922')[0]
    
    f_ovol.loc['IM', '512100'] = get_f_ratio('512100')[0]

    f_ovol.to_csv('/A/pk_tracker/f_ovol.csv')

    print('F_ovol Table Update Complete!')

else:

    print('No Need to Update F_ovol!')


tc = {
    '510050': round(1/get_e_ratio('510050'),5),
    '510300': round(1/get_e_ratio('510300'),5),
    '510310': round(1/get_e_ratio('510310'),5),
    '510330': round(1/get_e_ratio('510330'),5),
    '159919': round(1/get_e_ratio('159919'),5),
    '510500': round(1/get_e_ratio('510500'),5),
    '159915': round(1/get_e_ratio('159915'),5),
    '159952': round(1/get_e_ratio('159952'),5),
    '159922': round(1/get_e_ratio('159922'),5),
    '588000': round(1/get_e_ratio('588000'),5),
    '588080': round(1/get_e_ratio('588080'),5),
    '512100': 1.,
    'IH': round(get_f_ratio('510050', False)[0],5),
    'IF': round(get_f_ratio('510300', False)[0],5),
    'IC': round(get_f_ratio('510500', False)[0],5),
    'IM': round(get_f_ratio('512100', False)[0],5),
}

pd.DataFrame(pd.Series(tc), columns=['tc']).to_csv('/A/pk_tracker/tc.csv')

print('TC Table Update Complete!')


