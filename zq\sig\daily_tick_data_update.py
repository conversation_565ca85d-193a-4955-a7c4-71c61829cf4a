from nb_common import *
from scipy.optimize import newton
from scipy.stats import norm
import os
import calendar
import math
from joblib import Parallel, delayed
from tqdm import tqdm
from scipy.optimize import minimize
from ut_redis import FrRedis

rd170 = FrRedis("************")

pd.set_option('display.max_rows', 1000)
pd.set_option('display.max_columns', 1000)

jq.auth("***********", "Frtz8888")
print(jq.get_query_count())

jq.auth("***********", "Frtz8888") 
print(jq.get_query_count())


def download_etf(underlying):

    all_t_days = np.array(pickle_read_tdays()[:])
    today = datetime.datetime.now().date()

    already_exist = set([con.split('.')[0] for con in np.array(os.listdir('/A/MD/tick/s/raw/'))])
    
    if underlying not in already_exist:
        st_dt = '2020-01-01'
    else:
        old_file = pd.read_pickle('/A/MD/tick/s/raw/'+underlying+'.pickle')
        ext_dts = np.sort(np.unique([dt.date() for dt in old_file.index.to_pydatetime()]))

        missing_dts = [ext_dts[-1]]
        old_st_dt_idx = np.argwhere(all_t_days == ext_dts[0])[0][0]
        for dt in all_t_days[old_st_dt_idx:]:
            if dt not in ext_dts:
                missing_dts.append(dt)
        missing_dts = np.sort(missing_dts)
        st_dt = missing_dts[0]

    new_file = jq.get_ticks(security=jq.normalize_code(underlying), start_dt=str(st_dt)+' 09:00:00', end_dt=str(today)+' 15:30:00', \
                      fields=[
                          'time','current','high','low','volume','money', \
                          'a1_p','a2_p','a3_p','a4_p','a5_p', \
                          'a1_v','a2_v','a3_v','a4_v','a5_v', \
                          'b1_p','b2_p','b3_p','b4_p','b5_p', \
                          'b1_v','b2_v','b3_v','b4_v','b5_v', 
                      ], skip=False,df=True).set_index('time')

    if underlying not in already_exist:
        combined_file = new_file
    else:
        old_file = old_file[old_file.index < str(st_dt)+' 01:00:00']
        combined_file = pd.concat((old_file, new_file))
        
    # print("xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", '/A/MD/tick/s/raw/'+str(underlying)+'.pickle')

    combined_file.to_pickle('/A/MD/tick/s/raw/'+str(underlying)+'.pickle')

    
    # df = pd.read_pickle("/A/MD/tick/s/raw/510050.pickle")
    # df.to_pickle("/A/MD/tick/s/raw/510050.pickle")
    
    print(underlying,'Download and Replenish Complete!')



def process_etf(underlying):

    all_t_days = pickle_read_tdays()[:]

    already_exist = set([con.split('.')[0] for con in np.array(os.listdir('/A/MD/tick/s/processed/'))])

    if underlying not in already_exist:

        etfdata = pd.read_pickle('/A/MD/tick/s/raw/'+underlying+'.pickle')

    else:

        old_file = pd.read_pickle('/A/MD/tick/s/processed/'+underlying+'.pickle')
        ext_dts = np.sort(np.unique([dt.date() for dt in old_file.index.to_pydatetime()]))

        missing_dts = [ext_dts[-1]]
        old_st_dt_idx = np.argwhere(all_t_days == ext_dts[0])[0][0]
        for dt in all_t_days[old_st_dt_idx:]:
            if dt not in ext_dts:
                missing_dts.append(dt)
        missing_dts = np.sort(missing_dts)
        st_dt = missing_dts[0]       

        old_file = old_file[old_file.index < str(st_dt)+' 01:00:00']

        raw_new_file = pd.read_pickle('/A/MD/tick/s/raw/'+underlying+'.pickle')
        etfdata = raw_new_file[raw_new_file.index > str(st_dt)+' 01:00:00'] 

    etfdata = etfdata.reset_index().drop_duplicates(subset=['time'], keep='first').set_index('time')

    etfdata = etfdata.resample('1s').ffill()

    st_date = etfdata.index[0].date()
    ed_date = etfdata.index[-1].date()
    st_date_idx = np.argwhere(all_t_days == st_date)[0][0] if st_date in all_t_days else 0
    ed_date_idx = np.argwhere(all_t_days == ed_date)[0][0] if ed_date in all_t_days else len(all_t_days)
    t_days = all_t_days[st_date_idx:ed_date_idx+1]

    all_data = []
    for date in tqdm(t_days):
        daily_data = pd.concat([etfdata[(etfdata.index>str(date)+' 09:30:00') & (etfdata.index<=str(date)+' 11:30:00')], \
                                etfdata[(etfdata.index>str(date)+' 13:00:00') & (etfdata.index<=str(date)+' 15:00:00')]])
        all_data.append(daily_data)

    all_data = pd.concat(all_data)

    if underlying in already_exist:
        all_data = pd.concat((old_file, all_data))
        
    all_data.to_pickle('/A/MD/tick/s/processed/'+str(underlying)+'.pickle')
    print(underlying, 'Processing Complete!')       

download_etf('510050')
download_etf('510300')
download_etf('510500')
download_etf('159915')
download_etf('588000')

process_etf('510050')
process_etf('510300')
process_etf('510500')
process_etf('159915')
process_etf('588000')



def get_op_info(underlying, include_just_exp=False):

    underlying_dict = {
        '510050': '50ETF',
        '510300': '300ETF',
        '159919': '919ETF',
        '510500': '500ETF',
        '159922': '922ETF',
        '159915': '915ETF',
        '588000': 'k50ETF',
        '588080': 'k58ETF',
    }
    
    dfoplst = pickle_read_oplst()
    dfoplst = dfoplst[(dfoplst.underlying_symbol.str.contains(underlying)) & (dfoplst.last_trade_date >= datetime.date(2020,1,1))].sort_values(by=['code'], ascending=False).reset_index(drop=True) 
    just_exp_codes = [code.split('.')[0] for code in dfoplst[dfoplst.last_trade_date == np.sort(np.unique(dfoplst.last_trade_date.values.flatten()))[-5]].code] 
                     # [code.split('.')[0] for code in dfoplst[dfoplst.last_trade_date == np.sort(np.unique(dfoplst.last_trade_date.values.flatten()))[-6]].code]

    already_downloaded = set([con.split('.')[0] for con in np.array(os.listdir('/A/MD/tick/o/raw/'+underlying))])

    today = datetime.datetime.now().date()

    to_download_idx = []
    to_replenish_idx = []
    for i in tqdm(range(dfoplst.shape[0])):
        
        op_code = dfoplst.code.iloc[i].split('.')[0]

        op_ftd = dfoplst.list_date.iloc[i]
        op_ltd = dfoplst.last_trade_date.iloc[i]

        if op_code not in already_downloaded:
            to_download_idx.append(i)
        else:
            if op_ltd >= today:
                to_replenish_idx.append(i)  

        if include_just_exp:
            if op_code in just_exp_codes:
                to_replenish_idx.append(i)

    dfoplst_d = (dfoplst.iloc[np.array(to_download_idx),:]).sort_values(by=['code'], ascending=False).reset_index(drop=True) 
    dfoplst_r = (dfoplst.iloc[np.array(to_replenish_idx),:]).sort_values(by=['code'], ascending=False).reset_index(drop=True) 

    return dfoplst_d, dfoplst_r



def get_contract_data(con_id, start_dt, end_dt):

    con_df = jq.get_ticks(security=con_id, start_dt=str(start_dt)+' 09:00:00', end_dt=str(end_dt)+' 15:30:00', \
                          fields=[
                              'time','current','high','low','volume','money','position', \
                              'a1_p','a2_p','a3_p','a4_p','a5_p', \
                              'a1_v','a2_v','a3_v','a4_v','a5_v', \
                              'b1_p','b2_p','b3_p','b4_p','b5_p', \
                              'b1_v','b2_v','b3_v','b4_v','b5_v', 
                          ], skip=False,df=True).set_index('time')

    return con_df



def download_op(dfoplst):

    if dfoplst.shape[0] > 0:

        underlying_dict = {
            '510050': '50ETF',
            '510300': '300ETF',
            '159919': '919ETF',
            '510500': '500ETF',
            '159922': '922ETF',
            '159915': '915ETF',
            '588000': 'k50ETF',
            '588080': 'k58ETF',
        }
    
        underlying = (dfoplst.underlying_symbol.iloc[0]).split('.')[0]
    
        prog_st_time = datetime.datetime.now()
        
        print('Running With First Account!')
        
        jq.auth("***********", "Frtz8888")
        
        row_num = 0
        
        if row_num < dfoplst.shape[0]:
            pbar = tqdm(total=dfoplst.shape[0])
        
        while 1:

            if row_num >= dfoplst.shape[0]:
                break

            if row_num < dfoplst.shape[0]:
                pbar.update(1)
            
            rem = jq.get_query_count()['spare']
        
            curr_time = datetime.datetime.now().time()
            
            con_id = dfoplst.code.iloc[row_num]
            start_dt = dfoplst.list_date.iloc[row_num]
            end_dt = dfoplst.last_trade_date.iloc[row_num]
        
            time1 = datetime.datetime.now()
    
            today = datetime.datetime.now()
            weekday = calendar.weekday(today.year, today.month, today.day)
            if weekday < 5:
                time_condition = curr_time > datetime.time(16,0,0) and curr_time < datetime.time(23,59,59)
            else:
                time_condition = curr_time > datetime.time(8,0,0) and curr_time < datetime.time(23,59,59)
        
            if time_condition and rem > ********:
              
                con_df = get_contract_data(con_id, start_dt, end_dt)
        
                con_df.to_pickle('/A/MD/tick/o/raw/'+underlying+'/'+str(con_id.split('.')[0])+'.pickle')
        
                time2 = datetime.datetime.now()
        
                print('No.'+str(row_num)+' row: '+str(con_id.split('.')[0])+' download complete! totol '+str(con_df.shape[0])+' row. '+str(rem)+ \
                      ' rows remaining! Takes '+str(round((time2 - time1).total_seconds(),3))+' seconds!')
        
                row_num += 1
                
            else:
    
                if rem > ********:
                    print('Sleeping!', str(rem)+' rows remaining!')
                    time.sleep(60)
                else:
                    break
                
        
        print('Running With Second Account!')
        
        jq.auth("***********", "Frtz8888") 

        if row_num < dfoplst.shape[0]:
            pbar = tqdm(total=dfoplst.shape[0])
        
        while 1:

            if row_num >= dfoplst.shape[0]:
                break

            if row_num < dfoplst.shape[0]:
                pbar.update(1)
            
            rem = jq.get_query_count()['spare']
        
            curr_time = datetime.datetime.now().time()
            
            con_id = dfoplst.code.iloc[row_num]
            start_dt = dfoplst.list_date.iloc[row_num]
            end_dt = dfoplst.last_trade_date.iloc[row_num]
        
            time1 = datetime.datetime.now()
    
            today = datetime.datetime.now()
            weekday = calendar.weekday(today.year, today.month, today.day)
            if weekday < 5:
                time_condition = curr_time > datetime.time(16,0,0) and curr_time < datetime.time(23,59,59)
            else:
                time_condition = curr_time > datetime.time(8,0,0) and curr_time < datetime.time(23,59,59)
        
            if time_condition and rem > ********:
            
                con_df = get_contract_data(con_id, start_dt, end_dt)
        
                con_df.to_pickle('/A/MD/tick/o/raw/'+underlying+'/'+str(con_id.split('.')[0])+'.pickle')
        
                time2 = datetime.datetime.now()
        
                print('No.'+str(row_num)+' row: '+str(con_id.split('.')[0])+' download complete! totol '+str(con_df.shape[0])+' row. '+str(rem)+ \
                      ' rows remaining! Takes '+str(round((time2 - time1).total_seconds(),3))+' seconds!')
        
                row_num += 1
                
            else:
        
                if rem > ******** :
                    print('Sleeping!', str(rem)+' rows remaining!')
                    time.sleep(60)
                else:
                    break
                
        prog_ed_time = datetime.datetime.now()
        print('In Total, '+str(round((prog_ed_time - prog_st_time).total_seconds()/60,3))+' Minutes Elapsed!')
        print(str(dfoplst.shape[0]-row_num), 'Not Yet Downloaded!')


def get_replenish_dt(df, act_st_dt, act_ed_dt):
    all_t_days = np.array(pickle_read_tdays()[:])

    if df.shape[0] > 0:
        existing_dt = np.sort(np.unique([dt.date() for dt in df.index.to_pydatetime()]))
    else:
        existing_dt = []

    act_st_dt_idx = np.argwhere(all_t_days == act_st_dt)[0][0] if act_st_dt in all_t_days else 0
    act_ed_dt_idx = np.argwhere(all_t_days == act_ed_dt)[0][0] if act_ed_dt in all_t_days else all_t_days.shape[0] - 1

    dts_to_check = all_t_days[act_st_dt_idx:act_ed_dt_idx + 1]

    to_replenish = [existing_dt[-1]] if len(existing_dt) > 0 else []
    for dt in dts_to_check:
        if dt not in existing_dt:
            to_replenish.append(dt)
    to_replenish = np.sort(to_replenish)

    return to_replenish


def replenish_op(dfoplst):
    if dfoplst.shape[0] > 0:

        underlying_dict = {
            '510050': '50ETF',
            '510300': '300ETF',
            '159919': '919ETF',
            '510500': '500ETF',
            '159922': '922ETF',
            '159915': '915ETF',
            '588000': 'k50ETF',
            '588080': 'k58ETF',
        }

        underlying = (dfoplst.underlying_symbol.iloc[0]).split('.')[0]

        prog_st_time = datetime.datetime.now()

        print('Running With First Account!')

        jq.auth("***********", "Frtz8888")

        row_num = 0

        if row_num < dfoplst.shape[0]:
            pbar = tqdm(total=dfoplst.shape[0])

        corrupt_cons = []

        while 1:

            try:

                if row_num >= dfoplst.shape[0]:
                    break

                if row_num < dfoplst.shape[0]:
                    pbar.update(1)

                rem = jq.get_query_count()['spare']

                con_id = dfoplst.code.iloc[row_num]

                curr_time = datetime.datetime.now().time()
                today = datetime.datetime.now()
                weekday = calendar.weekday(today.year, today.month, today.day)
                if weekday < 5:
                    time_condition = curr_time > datetime.time(16, 0, 0) and curr_time < datetime.time(23, 59, 59)
                else:
                    time_condition = curr_time > datetime.time(8, 0, 0) and curr_time < datetime.time(23, 59, 59)

                if time_condition and rem > ********:

                    time1 = datetime.datetime.now()

                    downloaded_file = pd.read_pickle(
                        '/A/MD/tick/o/raw/' + underlying + '/' + con_id.split('.')[0] + '.pickle')
                    act_start_dt, act_end_dt = dfoplst.list_date.iloc[row_num], dfoplst.last_trade_date.iloc[row_num]
                    dts_to_download = get_replenish_dt(downloaded_file, act_start_dt, act_end_dt)

                    start_dt, end_dt = dts_to_download[0], act_end_dt

                    if downloaded_file.shape[0] > 0:
                        downloaded_file = downloaded_file[downloaded_file.index < str(start_dt) + ' 01:00:00']

                    con_df = get_contract_data(con_id, start_dt, end_dt)

                    con_df = pd.concat([downloaded_file, con_df]) if downloaded_file.shape[0] > 0 else con_df

                    con_df.to_pickle(
                        '/A/MD/tick/o/raw/' + underlying + '/' + str(con_id.split('.')[0]) + '.pickle')

                    time2 = datetime.datetime.now()

                    print('No.' + str(row_num) + ' row: ' + str(con_id.split('.')[0]) + ' download complete! totol ' + str(
                        con_df.shape[0]) + ' rows. ' + str(rem) + \
                          ' rows remaining! From ' + str(start_dt) + ' to ' + str(end_dt) + '. Takes ' + str(
                        round((time2 - time1).total_seconds(), 3)) + ' seconds!')

                    row_num += 1

                else:

                    if rem > ********:
                        print('Sleeping!', str(rem) + ' rows remaining!')
                        time.sleep(60)
                    else:
                        break

            except:
                con_id = dfoplst.code.iloc[row_num]
                corrupt_cons.append(con_id)
                row_num += 1

        print('Running With Second Account!')

        jq.auth("***********", "Frtz8888")

        if row_num < dfoplst.shape[0]:
            pbar = tqdm(total=dfoplst.shape[0])

        while 1:

            try:

                if row_num >= dfoplst.shape[0]:
                    break

                if row_num < dfoplst.shape[0]:
                    pbar.update(1)

                rem = jq.get_query_count()['spare']

                con_id = dfoplst.code.iloc[row_num]

                curr_time = datetime.datetime.now().time()
                today = datetime.datetime.now()
                weekday = calendar.weekday(today.year, today.month, today.day)
                if weekday < 5:
                    time_condition = curr_time > datetime.time(16, 0, 0) and curr_time < datetime.time(23, 59, 59)
                else:
                    time_condition = curr_time > datetime.time(8, 0, 0) and curr_time < datetime.time(23, 59, 59)

                if time_condition and rem > ********:

                    time1 = datetime.datetime.now()

                    downloaded_file = pd.read_pickle(
                        '/A/MD/tick/o/raw/' + underlying + '/' + con_id.split('.')[0] + '.pickle')
                    act_start_dt, act_end_dt = dfoplst.list_date.iloc[row_num], dfoplst.last_trade_date.iloc[row_num]
                    dts_to_download = get_replenish_dt(downloaded_file, act_start_dt, act_end_dt)

                    start_dt, end_dt = dts_to_download[0], act_end_dt

                    if downloaded_file.shape[0] > 0:
                        downloaded_file = downloaded_file[downloaded_file.index < str(start_dt) + ' 01:00:00']

                    con_df = get_contract_data(con_id, start_dt, end_dt)

                    con_df = pd.concat([downloaded_file, con_df]) if downloaded_file.shape[0] > 0 else con_df

                    con_df.to_pickle(
                        '/A/MD/tick/o/raw/' + underlying + '/' + str(con_id.split('.')[0]) + '.pickle')

                    time2 = datetime.datetime.now()

                    print('No.' + str(row_num) + ' row: ' + str(con_id.split('.')[0]) + ' download complete! totol ' + str(
                        con_df.shape[0]) + ' rows. ' + str(rem) + \
                          ' rows remaining! From ' + str(start_dt) + ' to ' + str(end_dt) + '. Takes ' + str(
                        round((time2 - time1).total_seconds(), 3)) + ' seconds!')

                    row_num += 1

                else:

                    if rem > ********:
                        print('Sleeping!', str(rem) + ' rows remaining!')
                        time.sleep(60)
                    else:
                        break

            except:
                con_id = dfoplst.code.iloc[row_num]
                corrupt_cons.append(con_id)
                row_num += 1
                

        prog_ed_time = datetime.datetime.now()
        print('In Total, ' + str(round((prog_ed_time - prog_st_time).total_seconds() / 60, 3)) + ' Minutes Elapsed!')
        print(str(dfoplst.shape[0] - row_num), 'Not Yet Downloaded!')
                
        prog_ed_time = datetime.datetime.now()
        print('In Total, '+str(round((prog_ed_time - prog_st_time).total_seconds()/60,3))+' Minutes Elapsed!')
        print(str(dfoplst.shape[0]-row_num), 'Not Yet Downloaded!')

        print('Total Corrupt Files: '+str(len(corrupt_cons)))
        if len(corrupt_cons) > 0:
            for con in corrupt_cons:
                file_path = '/A/MD/tick/o/raw/' + underlying + '/' + str(con.split('.')[0]) + '.pickle'
                os.remove(file_path)
                print('################## ATTENTION ##################',str(con), 'Deleted due to file corruption!' )
        # return corrupt_cons


include_just_exp = False  

dfoplst_d, dfoplst_r = get_op_info(underlying='510050', include_just_exp=include_just_exp)
print(dfoplst_d.shape[0],'Rows to Download!')
print(dfoplst_r.shape[0],'Rows to Replenish!')
download_op(dfoplst_d)     
replenish_op(dfoplst_r)
dfoplst_d, dfoplst_r = get_op_info(underlying='510050', include_just_exp=include_just_exp)
download_op(dfoplst_d)  

dfoplst_d, dfoplst_r = get_op_info(underlying='510300', include_just_exp=include_just_exp)
print(dfoplst_d.shape[0],'Rows to Download!')
print(dfoplst_r.shape[0],'Rows to Replenish!')
download_op(dfoplst_d)
replenish_op(dfoplst_r)
dfoplst_d, dfoplst_r = get_op_info(underlying='510300', include_just_exp=include_just_exp)
download_op(dfoplst_d)


dfoplst_d, dfoplst_r = get_op_info(underlying='510500', include_just_exp=include_just_exp)
print(dfoplst_d.shape[0],'Rows to Download!')
print(dfoplst_r.shape[0],'Rows to Replenish!')
download_op(dfoplst_d)
replenish_op(dfoplst_r)
dfoplst_d, dfoplst_r = get_op_info(underlying='510500', include_just_exp=include_just_exp)
download_op(dfoplst_d)


dfoplst_d, dfoplst_r = get_op_info(underlying='159915', include_just_exp=include_just_exp)
print(dfoplst_d.shape[0],'Rows to Download!')
print(dfoplst_r.shape[0],'Rows to Replenish!')
download_op(dfoplst_d)
replenish_op(dfoplst_r)
dfoplst_d, dfoplst_r = get_op_info(underlying='159915', include_just_exp=include_just_exp)
download_op(dfoplst_d)


dfoplst_d, dfoplst_r = get_op_info(underlying='588000', include_just_exp=include_just_exp)
print(dfoplst_d.shape[0],'Rows to Download!')
print(dfoplst_r.shape[0],'Rows to Replenish!')
download_op(dfoplst_d)
replenish_op(dfoplst_r)
dfoplst_d, dfoplst_r = get_op_info(underlying='588000', include_just_exp=include_just_exp)
download_op(dfoplst_d)




etf50_df = pd.read_pickle('/A/MD/tick/s/processed/510050.pickle')
etf300_df = pd.read_pickle('/A/MD/tick/s/processed/510300.pickle')
etf500_df = pd.read_pickle('/A/MD/tick/s/processed/510500.pickle')
etf915_df = pd.read_pickle('/A/MD/tick/s/processed/159915.pickle')
etfk50_df = pd.read_pickle('/A/MD/tick/s/processed/588000.pickle')

etf_dict = {
    '510050': etf50_df,
    '510300': etf300_df,
    '510500': etf500_df,
    '159915': etf915_df,
    '588000': etfk50_df,
}

def calc_black_scholes_price_c(S, K, T, r, sigma):  
    d1 = (np.log(S / K) + (r + 0.5 * sigma ** 2) * T) / (sigma * np.sqrt(T))  
    d2 = d1 - sigma * np.sqrt(T)  

    return S * norm.cdf(d1, 0.0, 1.0) - K * np.exp(-r * T) * norm.cdf(d2, 0.0, 1.0)  



def calc_black_scholes_price_p(S, K, T, r, sigma):  
    d1 = (np.log(S / K) + (r + 0.5 * sigma ** 2) * T) / (sigma * np.sqrt(T))  
    d2 = d1 - sigma * np.sqrt(T)  

    return K * np.exp(-r * T) * norm.cdf(-d2, 0.0, 1.0) - S * norm.cdf(-d1, 0.0, 1.0)  



def implied_volatility_c(market_price, S, K, T, r, x0=0.2, tol=1e-4):  
    def func(x):  
        return calc_black_scholes_price_c(S, K, T, r, x) - market_price  
    iv = newton(func, x0, tol=tol)
    return iv


def implied_volatility_p(market_price, S, K, T, r, x0=0.2, tol=1e-4):  
    def func(x):  
        return calc_black_scholes_price_p(S, K, T, r, x) - market_price  
    iv = newton(func, x0, tol=tol)
    return iv


def compute_delta_c(etf_price, iv, K, T, r):

    delta = norm.cdf(((np.log(etf_price/K)+(r+0.5*iv**2)*T)/(iv*np.sqrt(T))),0.0,1.0)

    return delta



def compute_delta_p(etf_price, iv, K, T, r):

    delta = norm.cdf(((np.log(etf_price/K)+(r+0.5*iv**2)*T)/(iv*np.sqrt(T))),0.0,1.0) - 1

    return delta



def compute_theta_c(etf_price, imp_vol, k, T, r):

    d1 = (np.log(etf_price / k) + (r + 0.5 * imp_vol ** 2) * T) / (imp_vol * np.sqrt(T))

    d2 = d1 - imp_vol * T ** 0.5

    delta = norm.cdf(d1, 0.0, 1.0)

    phi = np.exp(-1 * d1 ** 2 / 2) / (2 * np.pi) ** 0.5

    theta = (1e4/365)*((-1 * etf_price * phi * imp_vol) / (2 * T ** 0.5) - r * k * np.exp(-1 * r * T) * norm.cdf(d2, 0.0, 1.0))

    return theta



def compute_theta_p(etf_price, imp_vol, k, T, r):

    d1 = (np.log(etf_price / k) + (r + 0.5 * imp_vol ** 2) * T) / (imp_vol * np.sqrt(T))

    d2 = d1 - imp_vol * T ** 0.5

    delta = norm.cdf(d1, 0.0, 1.0) - 1

    phi = np.exp(-1 * d1 ** 2 / 2) / (2 * np.pi) ** 0.5

    theta = (1e4/365)*((-1 * etf_price * phi * imp_vol) / (2 * T ** 0.5) - r * k * np.exp(-1 * r * T) * norm.cdf(d2, 0.0, 1.0))

    return theta



def get_each_file(opdata):

    all_t_days = pickle_read_tdays()[:]

    opdata['spread'] = (opdata.a1_p - opdata.b1_p)
    opdata = opdata.resample('1s').ffill()

    st_date = opdata.index[0].date()
    ed_date = opdata.index[-1].date()
    st_date_idx = np.argwhere(all_t_days == st_date)[0][0] if st_date in all_t_days else 0
    ed_date_idx = np.argwhere(all_t_days == ed_date)[0][0] if ed_date in all_t_days else len(all_t_days)
    t_days = all_t_days[st_date_idx:ed_date_idx+1]

    all_data = []
    for date in t_days:
        daily_data = pd.concat([opdata[(opdata.index>str(date)+' 09:30:00') & (opdata.index<=str(date)+' 11:30:00')], \
                                opdata[(opdata.index>str(date)+' 13:00:00') & (opdata.index<=str(date)+' 15:00:00')]])
        all_data.append(daily_data)

    return pd.concat(all_data)



def add_greeks(df, con, etf_df):

    con_st, con_ed = str(df.index[0]), str(df.index[-1])

    df['midprice'] = (df.a1_p + df.b1_p) / 2
    
    invalid_idx = np.argwhere((df.current.values < 1e-8) | (np.isnan(df.current.values) == True)).flatten()
    curr_price = df.current.values.copy()
    mid_price = df.midprice.values.copy()
    curr_price[invalid_idx] = mid_price[invalid_idx]
    df['latest'] = curr_price
    
    dfoplst = pickle_read_oplst()
    dfoplst = dfoplst[(dfoplst.last_trade_date >= datetime.date(2020,1,1))].sort_values(by=['code'], ascending=False).reset_index(drop=True)
    seg_dfoplst = dfoplst[dfoplst.code.str.contains(con.split('.')[0])]

    op_type = seg_dfoplst.contract_type.iloc[0][:1]
    
    ex_dt = np.repeat(datetime.datetime.strptime(str(seg_dfoplst.last_trade_date.iloc[0])+' 16:00:00', '%Y-%m-%d %H:%M:%S'), df.shape[0])
    
    T = np.array([dt_obj.total_seconds() for dt_obj in (ex_dt - df.index.to_pydatetime())]) / (365*24*60*60)
    
    K = np.repeat(seg_dfoplst.exercise_price.iloc[0], df.shape[0])
    
    seg_etf_price_df = etf_df[(etf_df.index >= con_st) & (etf_df.index <= con_ed)]
    etf_con_st, etf_con_ed = str(seg_etf_price_df.index[0]), str(seg_etf_price_df.index[-1])
    etf_price = seg_etf_price_df.current.values

    if etf_con_st == con_st and etf_con_ed != con_ed:
        etf_price = np.concatenate((etf_price, np.repeat(np.nan, df.shape[0]-etf_price.shape[0])))
        print(con+' Ending Time Not Matched! Filled with NaNs!')
    if etf_con_st != con_st and etf_con_ed == con_ed:
        etf_price = np.concatenate((np.repeat(np.nan, df.shape[0]-etf_price.shape[0]), etf_price))
        print(con+' Starting Time Not Matched! Filled with NaNs!')
         
    op_price = df.latest.values

    r = np.array([0.02]*df.shape[0])
    
    x0 = np.array([0.2]*df.shape[0])

    if etf_price.shape[0] == op_price.shape[0]:

        if op_type == 'C':
            iv = implied_volatility_c(
                 market_price = op_price,
                 S = etf_price,
                 K = K,
                 T = T,
                 r = r,
                 x0 = x0,
                 tol=1e-4
            )
            iv[iv > 10] = np.nan
            iv[iv <= 0] = np.nan
            iv = pd.Series(iv).ffill().values.flatten()
            delta = compute_delta_c(etf_price, iv, K, T, r)
            theta = compute_theta_c(etf_price, iv, K, T, r)
        else:  
            iv = implied_volatility_p(
                 market_price = op_price,
                 S = etf_price,
                 K = K,
                 T = T,
                 r = r,
                 x0 = x0,
                 tol=1e-4
            )
            iv[iv > 10] = np.nan
            iv[iv <= 0] = np.nan
            iv = pd.Series(iv).ffill().values.flatten()
            delta = compute_delta_p(etf_price, iv, K, T, r)
            theta = compute_theta_p(etf_price, iv, K, T, r)
    
    
        df['iv'] = iv
        df['delta'] = delta
        df['theta'] = theta
    
    else:

        df['iv'] = np.nan
        df['delta'] = np.nan
        df['theta'] = np.nan      

        print('###### ERROR Option Trading Time Range: '+str(con)+' '+str(con_st)+' to '+str(con_ed)+' ######')
    
    df = df[[
        'current',
        'midprice',
        'latest',
        'high',
        'low',
        'volume',
        'money',
        'position',
        'a1_p',
        'a2_p',
        'a3_p',
        'a4_p',
        'a5_p',
        'a1_v',
        'a2_v',
        'a3_v',
        'a4_v',
        'a5_v',
        'b1_p',
        'b2_p',
        'b3_p',
        'b4_p',
        'b5_p',
        'b1_v',
        'b2_v',
        'b3_v',
        'b4_v',
        'b5_v',
        'spread',
        'iv',
        'delta',
        'theta'
    ]]

    return df


def get_con_to_process(underlying, whether_replenish_data=True, whether_near=False, process_all=False, include_just_exp=False):

    underlying_dict = {
        '510050': '50ETF',
        '510300': '300ETF',
        '510500': '500ETF',
        '159915': '915ETF',
        '588000': 'k50ETF'
    }

    dfoplst = pickle_read_oplst()
    dfoplst = dfoplst[(dfoplst.underlying_symbol.str.contains(underlying)) & (dfoplst.last_trade_date >= datetime.date(2020,1,1))].sort_values(by=['code'], ascending=False).reset_index(drop=True) 
    just_exp_cons = [code.split('.')[0] for code in dfoplst[dfoplst.last_trade_date == np.sort(np.unique(dfoplst.last_trade_date.values.flatten()))[-5]].code] 
                     # [code.split('.')[0] for code in dfoplst[dfoplst.last_trade_date == np.sort(np.unique(dfoplst.last_trade_date.values.flatten()))[-6]].code]
    
    dfoplst = pickle_read_oplst()
    dfoplst = dfoplst[(dfoplst.underlying_symbol.str.contains(underlying)) & (dfoplst.last_trade_date >= datetime.date(2020,1,1))]\
              .sort_values(by=['code'], ascending=False).reset_index(drop=True) 

    near_cons = np.sort(dfoplst.last_trade_date.unique())[-4:-2]
    
    today = datetime.datetime.now().date()

    raw_con_lst = set(os.listdir('/A/MD/tick/o/raw/'+underlying))

    processed_con_lst = set(os.listdir('/A/MD/tick/o/processed/'+underlying))

    if process_all:

        to_process = raw_con_lst

    else:
    
        to_process = []
    
        for con in raw_con_lst:
    
            exp_dt = dfoplst[dfoplst.code.str.contains(con.split('.')[0])].last_trade_date.iloc[0]
    
            if whether_replenish_data:
                process_condition = con not in processed_con_lst or exp_dt >= today
            else:
                process_condition = con not in processed_con_lst

            if whether_near:
                process_condition = process_condition and exp_dt in near_cons

            if include_just_exp:
                process_condition = process_condition or (con.split('.')[0] in just_exp_cons)
    
            if process_condition:
    
                to_process.append(con)

    return to_process


def process_op(underlying, whether_replenish_data=True, whether_near=False, process_all=False, include_just_exp=False):

    underlying_dict = {
        '510050': '50ETF',
        '510300': '300ETF',
        '510500': '500ETF',
        '159915': '915ETF',
        '588000': 'k50ETF',
    }
    
    all_con = get_con_to_process(underlying, whether_replenish_data, whether_near, process_all, include_just_exp)

    print(len(all_con), 'Contracts to Process!')
    
    for con in tqdm(all_con):

        # print(con)

        opdata = pd.read_pickle('/A/MD/tick/o/raw/'+underlying+'/'+con).reset_index().drop_duplicates(subset=['time'], keep='first').set_index('time')
        opdata = opdata[opdata.index <= str(etf_dict[underlying].index[-1]).split()[0]+' 16:00:00' ]
        
        if opdata.shape[0] > 0:
            proc_data = add_greeks(get_each_file(opdata), con, etf_dict[underlying])
            proc_data.to_pickle('/A/MD/tick/o/processed/'+underlying+'/'+con)
        else:
            print('###### ERROR: '+str(con.split('.')[0])+' After Set Last Trading Date! ######')



include_just_exp = False

process_op(underlying='510050', whether_replenish_data=True, whether_near=False, process_all=False, include_just_exp=include_just_exp)

process_op(underlying='510300', whether_replenish_data=True, whether_near=False, process_all=False, include_just_exp=include_just_exp)

process_op(underlying='510500', whether_replenish_data=True, whether_near=False, process_all=False, include_just_exp=include_just_exp)

process_op(underlying='159915', whether_replenish_data=True, whether_near=False, process_all=False, include_just_exp=include_just_exp)

process_op(underlying='588000', whether_replenish_data=True, whether_near=False, process_all=False, include_just_exp=include_just_exp)

print('COMPLETE!')