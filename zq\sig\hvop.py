import traceback

from nb_common import *
from ut_redis import FrRedis
import math
from scipy.optimize import minimize
from scipy.stats import norm
from scipy.optimize import newton
from sys import platform

underlying_dict = {
    '510050': ['50', 'n50', 'h50'],
    '510300': ['300', 'n300', 'h300'],
    '510500': ['500', 'n500', 'h500'],
    '159919': ['919', 'n919', 'h919'],
    '159922': ['922', 'n922', 'h922'],
    '588000': ['k50', 'nk50', 'hk50'],
    '588080': ['k58', 'nk58', 'hk58'],
    '159915': ['915', 'n915', 'h915'],
    '159901': ['901', 'n901', 'h901'],
}


SEVER53 = r'\\***********\share\MD' if sys == 'win32' else '/A/MD/'

rd170 = FrRedis("************")
rd115 = FrRedis("************")
rd181 = FrRedis("************:6379:9")


def publish_manifest(manifest: dict, vopkey):
    """ 调仓 """
    d = manifest.copy()

    existing_archives = rd170.hkeys(f"{vopkey}:archives")
    ver_arr = np.array(existing_archives).astype(int) if existing_archives is not None else []
    if len(ver_arr) > 0:
        last_ver = str(ver_arr.max())
        last_manifest = eval(rd170.hget(f"{vopkey}:archives", last_ver))
        last_manifest.pop('ts')
        last_manifest.pop('ver')
    else:
        last_manifest = None

    if not last_manifest or str(d) != str(last_manifest):
        tcver = rd170.incr(f"{vopkey}:tcvergen")  # 版本生成器
        rd170.set(f"{vopkey}:manifest", str(manifest))
        rd170.set(f"{vopkey}:tcver", tcver)  # 异常事件可能导致tcver与tcvergen不一致, 保险起见, 保存一个副本

        d['ts'] = time.strftime("%Y-%m-%d %H:%M:%S")
        d['ver'] = tcver

        rd170.hset(f"{vopkey}:archives", tcver, str(d))
        rd170.publish(f"{vopkey}:tc", tcver)




def get_hvop_info(underlying):

    vopname, nvopname, hvopname = underlying_dict[underlying]
    vopkey, nvopkey, hvopkey = f"ZQ:VOP:sig:{vopname}", f"ZQ:VOP:sig:{nvopname}", f"ZQ:VOP:sig:{hvopname}"

    manifest0 = rd170.get(f"{vopkey}:manifest")
    manifest0 = eval(manifest0) if manifest0 is not None else {}
    manifest0 = {k:-v for k, v in manifest0.items()}
    manifest1 = rd170.get(f"{nvopkey}:manifest")
    manifest1 = eval(manifest1) if manifest1 is not None else {}

    manifest0.update(manifest1)
    manifest = manifest0
    
    jz0, jz1 = rd170.get(f"{vopkey}:jz"), rd170.get(f"{nvopkey}:jz")
    jz0 = float(jz0) if jz0 is not None else 10000
    jz1 = float(jz1) if jz1 is not None else 10000
    jz = jz0 - jz1

    op_coef0, op_coef1 = rd170.get(f"{vopkey}:op_coef"), rd170.get(f"{nvopkey}:op_coef")
    op_coef0 = eval(op_coef0) if op_coef0 is not None else {'b':0, 's':0}
    op_coef1 = eval(op_coef1) if op_coef1 is not None else {'b':0, 's':0}
    op_coef = {     
        'b': min(op_coef0['b'], op_coef1['b']),
        's': min(op_coef0['s'], op_coef1['s']),
    }

    num_breaks0, num_breaks1 = rd170.get(f"{vopkey}:num_breaks"), rd170.get(f"{nvopkey}:num_breaks")
    num_breaks0 = float(num_breaks0) if num_breaks0 is not None else 0
    num_breaks1 = float(num_breaks1) if num_breaks1 is not None else 0
    num_breaks = num_breaks0 + num_breaks1

    tc_prog0, tc_prog1 = rd170.get(f"{vopkey}:tc_prog"), rd170.get(f"{nvopkey}:tc_prog")
    tc_prog0 = float(tc_prog0) if tc_prog0 is not None else 0
    tc_prog1 = float(tc_prog1) if tc_prog1 is not None else 0
    tc_prog = max(tc_prog0, tc_prog1)

    all_tv = rd170.get(f"{vopkey}:all_tv")
    all_tv = float(all_tv) if all_tv is not None else 0

    all_margin0 = rd170.get(f"{vopkey}:all_margin")
    all_margin1 = rd170.get(f"{nvopkey}:all_margin")
    all_margin0 = float(eval(all_margin0)['s']) if all_margin0 is not None else 0
    all_margin1 = float(eval(all_margin1)['b']) if all_margin1 is not None else 0
    all_margin = all_margin0 + all_margin1

    return {
        'manifest': manifest,
        'jz': jz*1e-2,
        'num_breaks': num_breaks,
        'tc_prog': tc_prog,
        'all_tv': all_tv,
        'all_margin': all_margin,
    }





def chain_update(dict1, dict2):
    
    dict1.update(dict2)

    return dict1
    




def main():

    vopname = 'index'
    index_vopkey = f"ZQ:VOP:sig:{vopname}"

    curr_time = datetime.datetime.now()
    yr, mon, day = curr_time.year, curr_time.month, curr_time.day

    preprare_intial_info_time = datetime.datetime(curr_time.year, curr_time.month, curr_time.day, 9, 0, 0, 0)
    openning_auction_starting_time = datetime.datetime(curr_time.year, curr_time.month, curr_time.day, 9, 15, 0, 0)
    openning_auction_ending_time = datetime.datetime(curr_time.year, curr_time.month, curr_time.day, 9, 27, 0, 0)
    openning_time = datetime.datetime(curr_time.year, curr_time.month, curr_time.day, 9, 30, 0, 0)
    noon_closing_time = datetime.datetime(curr_time.year, curr_time.month, curr_time.day, 11, 30, 0, 0)
    noon_openning_time = datetime.datetime(curr_time.year, curr_time.month, curr_time.day, 13, 0, 0, 0)
    closing_time = datetime.datetime(curr_time.year, curr_time.month, curr_time.day, 15, 0, 30, 0)
    rebalance_stop_time = datetime.datetime(curr_time.year, curr_time.month, curr_time.day, 14, 45, 0, 0)

    open_auction_update = False

    while 1:

        curr_time = datetime.datetime.now()

        if curr_time >= openning_auction_ending_time and not open_auction_update:

            open_auction_update = True

            hvop_50 = get_hvop_info('510050')
            hvop_300 = get_hvop_info('510300')
            hvop_500 = get_hvop_info('510500')
            hvop_915 = get_hvop_info('159915')
            hvop_k50 = get_hvop_info('588000')
            
            hvop_index_manifest = chain_update(chain_update(chain_update(chain_update(hvop_50['manifest'], hvop_300['manifest']), hvop_500['manifest']), hvop_915['manifest']), hvop_k50['manifest'])
            hvop_index_jz = hvop_50['jz'] + hvop_300['jz'] + hvop_500['jz'] + hvop_915['jz'] + hvop_k50['jz']
            hvop_index_num_breaks = max(hvop_50['num_breaks'], hvop_300['num_breaks'], hvop_500['num_breaks'], hvop_915['num_breaks'], hvop_k50['num_breaks'])
            hvop_index_tc_prog = max(hvop_50['tc_prog'], hvop_300['tc_prog'], hvop_500['tc_prog'], hvop_915['tc_prog'], hvop_k50['tc_prog'])
            hvop_index_tv = hvop_50['all_tv'] + hvop_300['all_tv'] + hvop_500['all_tv'] + hvop_915['all_tv'] + hvop_k50['all_tv']
            hvop_index_margin = hvop_50['all_margin'] + hvop_300['all_margin'] + hvop_500['all_margin'] + hvop_915['all_margin'] + hvop_k50['all_margin']  

            print('time: ', datetime.datetime.now(), ' | jz: ', '{:.1f}'.format(hvop_index_jz), \
                  ' | Num Breaks: ', '{:.2f}'.format(hvop_index_num_breaks), \
                  ' | Time Value: ', '{:.2f}'.format(hvop_index_tv), \
                  ' | Rebalance Progress: ', '{:.2f}'.format(hvop_index_tc_prog),'%', ' | Margin: ', '{:.2f}'.format(hvop_index_margin), 'w')
            

            publish_manifest(hvop_index_manifest, index_vopkey)
            rd170.set(f"{index_vopkey}:all_margin", hvop_index_margin)
            rd170.set(f"{index_vopkey}:jz", hvop_index_jz)
            rd170.set(f"{index_vopkey}:tc_prog", hvop_index_tc_prog)
            rd170.set(f"{index_vopkey}:num_breaks", hvop_index_num_breaks)
            rd170.set(f"{index_vopkey}:all_tv", hvop_index_tv) 

            curtvtimestr = f'H:{datetime.datetime.now().strftime("%H%M%S")}'
            rd181.hset(curtvtimestr, f'ZQ:VOP:sig:index', str(hvop_index_jz))

        if (curr_time >= openning_time and curr_time <= noon_closing_time) or \
                (curr_time >= noon_openning_time and curr_time <= closing_time): 
                  
            hvop_50 = get_hvop_info('510050')
            hvop_300 = get_hvop_info('510300')
            hvop_500 = get_hvop_info('510500')
            hvop_915 = get_hvop_info('159915')
            hvop_k50 = get_hvop_info('588000')
            
            hvop_index_manifest = chain_update(chain_update(chain_update(chain_update(hvop_50['manifest'], hvop_300['manifest']), hvop_500['manifest']), hvop_915['manifest']), hvop_k50['manifest'])
            hvop_index_jz = hvop_50['jz'] + hvop_300['jz'] + hvop_500['jz'] + hvop_915['jz'] + hvop_k50['jz']
            hvop_index_num_breaks = max(hvop_50['num_breaks'], hvop_300['num_breaks'], hvop_500['num_breaks'], hvop_915['num_breaks'], hvop_k50['num_breaks'])
            hvop_index_tc_prog = max(hvop_50['tc_prog'], hvop_300['tc_prog'], hvop_500['tc_prog'], hvop_915['tc_prog'], hvop_k50['tc_prog'])
            hvop_index_tv = hvop_50['all_tv'] + hvop_300['all_tv'] + hvop_500['all_tv'] + hvop_915['all_tv'] + hvop_k50['all_tv']
            hvop_index_margin = hvop_50['all_margin'] + hvop_300['all_margin'] + hvop_500['all_margin'] + hvop_915['all_margin'] + hvop_k50['all_margin']  

            print('time: ', datetime.datetime.now(), ' | jz: ', '{:.1f}'.format(hvop_index_jz), \
                  ' | Num Breaks: ', '{:.2f}'.format(hvop_index_num_breaks), \
                  ' | Time Value: ', '{:.2f}'.format(hvop_index_tv), \
                  ' | Rebalance Progress: ', '{:.2f}'.format(hvop_index_tc_prog),'%', ' | Margin: ', '{:.2f}'.format(hvop_index_margin), 'w')

            publish_manifest(hvop_index_manifest, index_vopkey)
            rd170.set(f"{index_vopkey}:all_margin", hvop_index_margin)
            rd170.set(f"{index_vopkey}:jz", hvop_index_jz)
            rd170.set(f"{index_vopkey}:tc_prog", hvop_index_tc_prog)
            rd170.set(f"{index_vopkey}:num_breaks", hvop_index_num_breaks)
            rd170.set(f"{index_vopkey}:all_tv", hvop_index_tv) 

            curtvtimestr = f'H:{datetime.datetime.now().strftime("%H%M%S")}'
            rd181.hset(curtvtimestr, f'ZQ:VOP:sig:index', str(hvop_index_jz))
            rd181.hset(curtvtimestr, f'ZQ:VOP:sig:manifest', str(hvop_index_manifest))

        if curr_time >= closing_time:
            break

        curr_time_float = time.time()
        next_time = int(curr_time_float + 1)
        sleep_amt = next_time - curr_time_float
        time.sleep(sleep_amt)

if __name__ == '__main__':

    main()