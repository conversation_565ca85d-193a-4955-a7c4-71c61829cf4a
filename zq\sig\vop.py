import traceback
from nb_common import *
from ut_redis import FrRedis
import math
from scipy.optimize import minimize
from scipy.stats import norm
from scipy.optimize import newton
from sys import platform



underlying_dict = {
    '510050': ['50', 'n50'],
    '510300': ['300', 'n300'],
    '510500': ['500', 'n500'],
    '159919': ['919', 'n919'],
    '159922': ['922', 'n922'],
    '588000': ['k50', 'nk50'],
    '588080': ['k58', 'nk58'],
    '159915': ['915', 'n915'],
    '159901': ['901', 'n901']
}

SEVER53 = r'\\***********\share\MD' if sys == 'win32' else '/A/MD/'

rd170 = FrRedis("************")
rd115 = FrRedis("************")
rd181 = FrRedis("************:6379:9")


def publish_manifest(vopkey, vopname, manifest: dict):
    """ 调仓 """
    tcver = rd170.incr(f"{vopkey}:tcvergen")  # 版本生成器
    rd170.set("ZQ:VOP:sig:" + vopname + ":manifest", str(manifest))
    rd170.set("ZQ:VOP:sig:" + vopname + ":tcver", tcver)  # 异常事件可能导致tcver与tcvergen不一致, 保险起见, 保存一个副本

    d = manifest.copy()
    d['ts'] = time.strftime("%Y-%m-%d %H:%M:%S")
    d['ver'] = tcver
    rd170.hset("ZQ:VOP:sig:" + vopname + ":archives", tcver, str(d))
    # print(f"publish {vopkey}:tc")
    rd170.publish(f"{vopkey}:tc", tcver)


def calc_black_scholes_price(S, K, T, r, sigma, cp='C'):
    d1 = (math.log(S / K) + (r + 0.5 * sigma ** 2) * T) / (sigma * math.sqrt(T))
    d2 = d1 - sigma * math.sqrt(T)

    if cp == 'C':
        return S * norm.cdf(d1, 0.0, 1.0) - K * math.exp(-r * T) * norm.cdf(d2, 0.0, 1.0)
    elif cp == 'P':
        return K * math.exp(-r * T) * norm.cdf(-d2, 0.0, 1.0) - S * norm.cdf(-d1, 0.0, 1.0)
    else:
        raise ValueError("bad cp")


def implied_volatility(market_price, S, K, T, r, cp='C', x0=0.2, tol=1e-4):
    def func(x):
        return calc_black_scholes_price(S, K, T, r, x, cp) - market_price

    try:
        iv = newton(func, x0, tol=tol)
        return iv
    except:
        return 0.0001


def get_preopen_op_info(underlying, contract_month):
    all_op_list = rd170.keys("OPLST:*")
    op_idx = []
    exp_dt = []
    contract = []
    strike = []
    option_type = []
    ma = []
    for con in all_op_list:
        op_info = rd170.hgetall(con)
        op_idx.append(op_info['InstrumentID'])
        exp_dt.append(datetime.datetime(int(op_info['ExpireDate'][:4]), int(op_info['ExpireDate'][4:6]),
                                        int(op_info['ExpireDate'][6:])))
        contract.append(op_info['UnderlyingInstrID'])
        strike.append(float(op_info['StrikePrice']))
        option_type.append(int(op_info['OptionsType']))
        ma.append('M' if 'M' in op_info['InstrumentCode'] else 'A')
    op_info = {}
    op_info['id'] = op_idx
    op_info['exp_dt'] = exp_dt
    op_info['contract'] = contract
    op_info['strike'] = strike
    op_info['option_type'] = option_type
    op_info['ma'] = ma
    op_info = pd.DataFrame(op_info)

    curr_mon = np.sort(op_info.exp_dt.unique())[contract_month]
    op_info = op_info[(op_info.contract == underlying) & (op_info.exp_dt == curr_mon)]
    op_info = op_info.set_index('id')
    if op_info.ma.unique().shape[0] == 2:
        op_info = op_info[op_info.ma == 'M']

    return op_info



def get_op_price(con, type='midprice', multiplier=1e-4):
    op_price = 0.
    if type == 'bid':
        op_price = rd170.hget(f"MD:01{con}", "BP1")
        op_price = 0. if op_price is None else float(op_price) * multiplier

    elif type == 'ask':
        op_price = rd170.hget(f"MD:01{con}", "SP1")
        op_price = 0. if op_price is None else float(op_price) * multiplier

    elif type == 'midprice':
        op_price_bid = rd170.hget(f"MD:01{con}", "BP1")
        op_price_bid = 0. if op_price_bid is None else float(op_price_bid) * multiplier

        op_price_ask = rd170.hget(f"MD:01{con}", "SP1")
        op_price_ask = 0. if op_price_ask is None else float(op_price_ask) * multiplier

        spread = op_price_ask - op_price_bid

        if spread < 30:
            op_price = (op_price_bid + op_price_ask) / 2
        else:
            op_price = rd170.hget(f"MD:01{con}", "Latest")
            op_price = 0. if op_price is None else float(op_price) * multiplier
            print('################## ABNORNAL: ' + str(con) + ' Fill With Latest ##################')
    if op_price < 1e-8 and type == 'midprice':
        op_price = rd170.hget(f"MD:01{con}", "Latest")
        op_price = 0. if op_price is None else float(op_price) * multiplier
        print('################## ABNORNAL: ' + str(con) + ' Fill With Latest ##################')
    if op_price < 1e-8 and type == 'midprice':
        op_price = rd170.hget(f"MD:01{con}", "PreSettle")
        op_price = 0. if op_price is None else float(op_price) * multiplier
        print('################## ABNORNAL: ' + str(con) + ' Fill With PreSettle ##################')
    return op_price



def compute_delta(op_info, con):
    delta = None

    etf_price_bp = rd115.get(f"KZ:S{op_info.loc[con, 'contract']}:BP1")
    etf_price_sp = rd115.get(f"KZ:S{op_info.loc[con, 'contract']}:SP1")
    etf_price_bp = 2. if etf_price_bp is None else int(etf_price_bp) / 1e4
    etf_price_sp = 2. if etf_price_sp is None else int(etf_price_sp) / 1e4
    etf_price = (etf_price_bp + etf_price_sp) / 2

    yr, mon, day = [int(elem) for elem in str(op_info.loc[con, 'exp_dt']).split()[0].split('-')]
    ex_dt = datetime.datetime(yr, mon, day, 16, 0, 0)
    T = (ex_dt - datetime.datetime.now()).total_seconds() / (365 * 24 * 60 * 60)
    r = 0.02

    cp = 'C' if op_info.loc[con, 'option_type'] == 1 else 'P'

    curr_op_price = get_op_price(con, 'midprice', 1e-4)

    if curr_op_price > 0:

        iv = implied_volatility(
            market_price=curr_op_price,
            S=etf_price,
            K=op_info.loc[con, 'strike'],
            T=T,
            r=r,
            cp=cp)

        if cp == 'C':
            delta = norm.cdf(
                ((math.log(etf_price / op_info.loc[con, 'strike']) + (r + 0.5 * iv ** 2) * T) / (iv * math.sqrt(T))),
                0.0,
                1.0)
        elif cp == 'P':
            delta = norm.cdf(
                ((math.log(etf_price / op_info.loc[con, 'strike']) + (r + 0.5 * iv ** 2) * T) / (iv * math.sqrt(T))),
                0.0,
                1.0) - 1
    else:
        delta = 0

    if delta is None:
        print('################## Delta Computation Error! ##################')
    return delta



def get_option_contracts_live(op_info, underlying):
    etf_price_bp = rd115.get(f"KZ:S{underlying}:BP1")
    etf_price_sp = rd115.get(f"KZ:S{underlying}:SP1")
    etf_price_bp = 2. if etf_price_bp is None else int(etf_price_bp) / 1e4
    etf_price_sp = 2. if etf_price_sp is None else int(etf_price_sp) / 1e4
    etf_price = (etf_price_bp + etf_price_sp) / 2

    op_info['distance'] = (op_info['strike'] - etf_price).abs()
    call_op_info = op_info[op_info.option_type == 1].sort_values(by='distance').iloc[:3, :].sort_values(by=['strike'])
    put_op_info = op_info[op_info.option_type == 2].sort_values(by='distance').iloc[:3, :].sort_values(by=['strike'],
                                                                                                       ascending=False)

    call_delta = [compute_delta(op_info, con) for con in call_op_info.index]

    put_delta = [compute_delta(op_info, con) for con in put_op_info.index]

    if call_delta[1] < 0.3:
        call1, call2 = call_op_info.index[:2]
    else:
        call1, call2 = call_op_info.index[-2:]

    if put_delta[1] > -0.3:
        put1, put2 = put_op_info.index[:2]
    else:
        put1, put2 = put_op_info.index[-2:]

    return call1, call2, put1, put2



def get_option_positions_live(op_info, con_info, all_cash=10000):
    c1, c2, p1, p2 = con_info

    c1_delta = compute_delta(op_info, c1)
    c2_delta = compute_delta(op_info, c2)
    p1_delta = compute_delta(op_info, p1)
    p2_delta = compute_delta(op_info, p2)

    c1_price = get_op_price(c1, type='midprice', multiplier=1)
    c2_price = get_op_price(c2, type='midprice', multiplier=1)
    p1_price = get_op_price(p1, type='midprice', multiplier=1)
    p2_price = get_op_price(p2, type='midprice', multiplier=1)

    objective = lambda x: abs(c1_delta * x[0] + c2_delta * x[1] + p1_delta * x[2] + p2_delta * x[3])

    cons = ({'type': 'ineq',
             'fun': lambda x: (c1_price * x[0] + c2_price * x[1] + p1_price * x[2] + p2_price * x[3]) - all_cash},
            {'type': 'ineq', 'fun': lambda x: (x[0] - x[1])},
            {'type': 'ineq', 'fun': lambda x: (x[2] - x[3])})

    bnds = ((0, None), (0, None), (0, None), (0, None))

    res = (minimize(objective, (0, 0, 0, 0), bounds=bnds, constraints=cons))['x']

    if res.max() > 30:
        res = min(30 / res.max(), 1) * res

    if sum(res) > 100:
        adj_res = (100 / sum(res)) * res
    else:
        adj_res = res
    adj_res = (adj_res * 10).astype(int) * 0.1

    holding_asset = (c1_price*adj_res[0] + c2_price*adj_res[1] + p1_price*adj_res[2] + p2_price*adj_res[3])
    
    if holding_asset > all_cash:
        print('Asset Used:', holding_asset, 'All Available:', all_cash)
        adj_res = (all_cash / (holding_asset+100)) * adj_res 
        adj_res = (adj_res * 10).astype(int) * 0.1

    return adj_res



def monitor_delta_exposure_live(c1_delta, c2_delta, p1_delta, p2_delta, c1_num, c2_num, p1_num, p2_num):
    delta_exposure = (c1_delta * c1_num + c2_delta * c2_num + p1_delta * p1_num + p2_delta * p2_num) / \
                     (c1_delta * c1_num + c2_delta * c2_num + abs(p1_delta) * p1_num + abs(p2_delta) * p2_num)

    return delta_exposure


def get_a5(op_info, underlying, con):
    etf_price_bp = rd115.get(f"KZ:S{underlying}:BP1")
    etf_price_sp = rd115.get(f"KZ:S{underlying}:SP1")
    etf_price_bp = 2000. if etf_price_bp is None else int(etf_price_bp) * 0.1
    etf_price_sp = 2000. if etf_price_sp is None else int(etf_price_sp) * 0.1
    etf_price = (etf_price_bp + etf_price_sp) / 2

    strike = op_info[op_info.index == con].strike.iloc[0]
    c_con, p_con = op_info[op_info.strike == strike].sort_values(by=['option_type']).index
    c_price, p_price = get_op_price(c_con, type='midprice', multiplier=1), get_op_price(p_con, type='midprice',
                                                                                        multiplier=1)
    a5 = (c_price - p_price + strike * 1e4) * 0.1 - etf_price

    return a5


def get_spread(con):
    bid, ask = get_op_price(con, type='bid', multiplier=1), get_op_price(con, type='ask', multiplier=1)
    spread = ask - bid

    return spread


def get_op_coef(c1, c2, p1, p2, vopkey):
    c1_ba1 = rd170.hget(f"MD:01{c1}", "BA1")
    c1_ba1 = 1. if c1_ba1 is None else float(c1_ba1)

    c2_ba1 = rd170.hget(f"MD:01{c2}", "BA1")
    c2_ba1 = 1. if c2_ba1 is None else float(c2_ba1)

    p1_ba1 = rd170.hget(f"MD:01{p1}", "BA1")
    p1_ba1 = 1. if p1_ba1 is None else float(p1_ba1)

    p2_ba1 = rd170.hget(f"MD:01{p2}", "BA1")
    p2_ba1 = 1. if p2_ba1 is None else float(p2_ba1)

    c1_sa1 = rd170.hget(f"MD:01{c1}", "SA1")
    c1_sa1 = 1. if c1_sa1 is None else float(c1_sa1)

    c2_sa1 = rd170.hget(f"MD:01{c2}", "SA1")
    c2_sa1 = 1. if c2_sa1 is None else float(c2_sa1)

    p1_sa1 = rd170.hget(f"MD:01{p1}", "SA1")
    p1_sa1 = 1. if c1_sa1 is None else float(p1_sa1)

    p2_sa1 = rd170.hget(f"MD:01{p2}", "SA1")
    p2_sa1 = 1. if c1_sa1 is None else float(p2_sa1)

    manifest_dict = rd170.get(f"{vopkey}:manifest")
    manifest_dict = eval(manifest_dict) if manifest_dict is not None else {}

    c1_bratio = np.clip(c1_ba1 / (manifest_dict[c1] + 0.1), 0, 10) if c1 in manifest_dict else 0
    c2_bratio = np.clip(c2_ba1 / (manifest_dict[c2] + 0.1), 0, 10) if c2 in manifest_dict else 0
    p1_bratio = np.clip(p1_ba1 / (manifest_dict[p1] + 0.1), 0, 10) if p1 in manifest_dict else 0
    p2_bratio = np.clip(p2_ba1 / (manifest_dict[p2] + 0.1), 0, 10) if p2 in manifest_dict else 0

    c1_sratio = np.clip(c1_sa1 / (manifest_dict[c1] + 0.1), 0, 10) if c1 in manifest_dict else 0
    c2_sratio = np.clip(c2_sa1 / (manifest_dict[c2] + 0.1), 0, 10) if c2 in manifest_dict else 0
    p1_sratio = np.clip(p1_sa1 / (manifest_dict[p1] + 0.1), 0, 10) if p1 in manifest_dict else 0
    p2_sratio = np.clip(p2_sa1 / (manifest_dict[p2] + 0.1), 0, 10) if p2 in manifest_dict else 0

    op_coef = {
        's': min(c1_bratio, c2_bratio, p1_bratio, p2_bratio),
        'b': min(c1_sratio, c2_sratio, p1_sratio, p2_sratio),
    }

    return op_coef



def get_time_value(underlying, op_info, con):
    etf_price = int(rd115.get(f"KZ:S{underlying}:LATEST")) / 1e4
    op_price = get_op_price(con, 'midprice', 1)

    op_type = op_info.loc[con, 'option_type']
    strike = op_info.loc[con, 'strike']

    if op_type == 1:

        intrinsic_value = max(etf_price - strike, 0) * 1e4

    elif op_type == 2:

        intrinsic_value = max(strike - etf_price, 0) * 1e4

    time_value = op_price - intrinsic_value

    return time_value / 100



def get_margin(underlying, op_info, direction, con):
    op_type = op_info.loc[con, 'option_type']

    strike = op_info.loc[con, 'strike']

    op_price = get_op_price(con, 'midprice', 1e-4)

    etf_price = int(rd115.get(f"KZ:S{underlying}:LATEST")) / 1e4

    con_tv = get_time_value(underlying, op_info, con) * 1e2 * 1e-4

    if direction == 'b':

        margin = op_price * 10000

    elif direction == 's':

        if op_type == 1:

            margin = (op_price + max(0.12 * etf_price - con_tv, 0.07 * etf_price)) * 10000

        else:

            margin = min(op_price + max(0.12 * etf_price - con_tv, 0.07 * strike), strike) * 10000

    return margin



def get_smooth_adj_schedule(dic1, dic2, num=600):
    smooth_adj = {}
    for con in dic1.keys():
        if con not in dic2:
            dic2[con] = 0
    for con in dic2.keys():
        if con not in dic1:
            dic1[con] = 0
    for con in dic1:
        smooth_adj[con] = np.linspace(dic1[con], dic2[con], num=num+1)
    return smooth_adj



full_info = pd.read_pickle(f'{MDLSTDIR}/all_securities/full.pickle')
all_options_ex_dates = sorted(full_info[(full_info.type == 'options') & (full_info.name.str[:2] == '10')].end_date.dt.strftime("%Y-%m-%d").unique())
all_options_ex_dates = np.array([datetime.datetime.strptime(date, "%Y-%m-%d").date() for date in all_options_ex_dates])

today = datetime.datetime.now().date()
next_ex_date = all_options_ex_dates[all_options_ex_dates>=datetime.datetime.now().date()][0]
remaining_time = (next_ex_date-today).total_seconds() / (24*60*60)

def main(underlying, contract_month):
    vopname = underlying_dict[underlying][contract_month]
    vopkey = f"ZQ:VOP:sig:{vopname}"

    curr_time = datetime.datetime.now()
    yr, mon, day = curr_time.year, curr_time.month, curr_time.day

    preprare_intial_info_time = datetime.datetime(curr_time.year, curr_time.month, curr_time.day, 9, 0, 0, 0)
    openning_auction_starting_time = datetime.datetime(curr_time.year, curr_time.month, curr_time.day, 9, 15, 0, 0)
    openning_auction_ending_time = datetime.datetime(curr_time.year, curr_time.month, curr_time.day, 9, 26, 0, 0)
    openning_time = datetime.datetime(curr_time.year, curr_time.month, curr_time.day, 9, 30, 0, 0)
    noon_closing_time = datetime.datetime(curr_time.year, curr_time.month, curr_time.day, 11, 30, 0, 0)
    noon_openning_time = datetime.datetime(curr_time.year, curr_time.month, curr_time.day, 13, 0, 0, 0)
    closing_time = datetime.datetime(curr_time.year, curr_time.month, curr_time.day, 15, 0, 30, 0)
    rebalance_stop_time = datetime.datetime(curr_time.year, curr_time.month, curr_time.day, 14, 45, 0, 0)

    whether_intitial_op_info = True
    whether_initialize_contracts = True

    op_info = None
    con_info = None
    pos_info = None
    all_available_op_con = set()

    a5_checker_lst = []
    a5_checker = False
    a5_checker_count = 0

    meltdown_checker = False

    pos_rebalance_count = 0
    con_rebalance_count = 0
    rebalance_benchmark = 0
    rebalance_complete = True
    whether_rebalance = False
    rebalance_scehdule = None
    rebalance_counter = 1
    ed_con_dict = None    
    old_c1, old_c2, old_p1, old_p2 = None, None, None, None

    jz = None
    remaining_cash = None

    d = rd170.get(f"{vopkey}:manifest")
    if d is None:
        c1, c2 = None, None
        p1, p2 = None, None
        c1_num, c2_num = None, None
        p1_num, p2_num = None, None
        c1_delta, c2_delta = None, None
        p1_delta, p2_delta = None, None
        c1_price, c2_price = None, None
        p1_price, p2_price = None, None
        remaining_cash = 0
        jz = 10000
        first_day = True
    else:
        d = eval(d)
        ks = list(d.keys())
        c1, c2 = ks[0], ks[1]
        p1, p2 = ks[2], ks[3]
        c1_num, c2_num = d[c1], d[c2]
        p1_num, p2_num = d[p1], d[p2]
        c1_delta, c2_delta = None, None
        p1_delta, p2_delta = None, None
        c1_price, c2_price = None, None
        p1_price, p2_pric = None, None
        remaining_cash = rd170.get(f"{vopkey}:cash")
        remaining_cash = float(remaining_cash) if remaining_cash is not None else 0
        jz = rd170.get(f"{vopkey}:jz")
        jz = float(jz) if jz is not None else 0
        first_day = False
        
    while 1:

        try:

            curr_time = datetime.datetime.now()

            if whether_intitial_op_info:
                if curr_time > preprare_intial_info_time:
                    op_info = get_preopen_op_info(underlying, contract_month)
                    all_available_op_con = op_info.index
                    whether_intitial_op_info = False
                    print('Options Contracts Info Ready!')

            if whether_initialize_contracts and curr_time >= openning_auction_ending_time:

                whether_initialize_contracts = False

                if not first_day:
                    c1_price = get_op_price(c1, type='midprice', multiplier=1)
                    c2_price = get_op_price(c2, type='midprice', multiplier=1)
                    p1_price = get_op_price(p1, type='midprice', multiplier=1)
                    p2_price = get_op_price(p2, type='midprice', multiplier=1)
                    jz = remaining_cash + (c1_price * c1_num + c2_price * c2_num + p1_price * p1_num + p2_price * p2_num)

                con_info = get_option_contracts_live(op_info, underlying)
                pos_info = get_option_positions_live(op_info, con_info, jz)

                c1, c2, p1, p2 = con_info
                c1_num, c2_num, p1_num, p2_num = pos_info

                c1_price = get_op_price(c1, type='midprice', multiplier=1)
                c2_price = get_op_price(c2, type='midprice', multiplier=1)
                p1_price = get_op_price(p1, type='midprice', multiplier=1)
                p2_price = get_op_price(p2, type='midprice', multiplier=1)

                c1_delta = compute_delta(op_info, c1)
                c2_delta = compute_delta(op_info, c2)
                p1_delta = compute_delta(op_info, p1)
                p2_delta = compute_delta(op_info, p2)

                remaining_cash = jz - (c1_price * c1_num + c2_price * c2_num + p1_price * p1_num + p2_price * p2_num)

                redis_dict = {
                    c1: c1_num,
                    c2: c2_num,
                    p1: p1_num,
                    p2: p2_num
                }
                
                op_coef_dict = get_op_coef(c1, c2, p1, p2, vopkey)

                all_time_value = get_time_value(underlying, op_info, c1) * c1_num + \
                                 get_time_value(underlying, op_info, c2) * c2_num + \
                                 get_time_value(underlying, op_info, p1) * p1_num + \
                                 get_time_value(underlying, op_info, p2) * p2_num

                all_margin_b = (get_margin(underlying, op_info, 'b', c1) * c1_num + \
                                get_margin(underlying, op_info, 'b', c2) * c2_num + \
                                get_margin(underlying, op_info, 'b', p1) * p1_num + \
                                get_margin(underlying, op_info, 'b', p2) * p2_num) / 1e4

                all_margin_s = (get_margin(underlying, op_info, 's', c1) * c1_num + \
                                get_margin(underlying, op_info, 's', c2) * c2_num + \
                                get_margin(underlying, op_info, 's', p1) * p1_num + \
                                get_margin(underlying, op_info, 's', p2) * p2_num) / 1e4

                all_margin = {'b': all_margin_b, 's': all_margin_s}


                tc_prog = 0.

                meltdown_checker_lst = np.array([
                    get_spread(c1),
                    get_spread(c2),
                    get_spread(p1),
                    get_spread(p2),
                ])
                num_meltdown = (meltdown_checker_lst[meltdown_checker_lst < 1e-8]).shape[0]
                
                publish_manifest(vopkey, vopname, redis_dict)

                rd170.publish(vopkey, str(jz))

                rd170.set(f"{vopkey}:jz", jz)
                rd170.set(f"{vopkey}:num_breaks", str(num_meltdown))
                rd170.set(f"{vopkey}:tc_prog", tc_prog)
                rd170.set(f"{vopkey}:op_coef", str(op_coef_dict))
                rd170.set(f"{vopkey}:all_margin", str(all_margin))
                rd170.set(f"{vopkey}:all_tv", all_time_value)

                curtvtimestr = f'H:{datetime.datetime.now().strftime("%H%M%S")}'
                rd181.hset(curtvtimestr, f'ZQ:VOP:sig:{vopname}', str(jz))
                
                print('time: ', datetime.datetime.now(), ' | jz: ', '{:.1f}'.format(jz), \
                      ' | call 1: ', c1, '{:.2f}'.format(c1_price), '{:.2f}'.format(c1_num), '{:.2f}'.format(c1_delta), \
                      ' | call 2: ', c2, '{:.2f}'.format(c2_price), '{:.2f}'.format(c2_num), '{:.2f}'.format(c2_delta), \
                      ' | put 1: ', p1, '{:.2f}'.format(p1_price), '{:.2f}'.format(p1_num), '{:.2f}'.format(p1_delta), \
                      ' | put 2: ', p2, '{:.2f}'.format(p2_price), '{:.2f}'.format(p2_num), '{:.2f}'.format(p2_delta), \
                      ' | Rebalance Progress: ', '{:.2f}'.format(tc_prog),'%', ' | Cash: ', '{:.2f}'.format(remaining_cash))
                
               
                    
            update_conditions = (curr_time >= openning_time and curr_time <= noon_closing_time) or \
                                (curr_time >= noon_openning_time and curr_time <= closing_time)


            if update_conditions:

                c1_price = get_op_price(c1, type='midprice', multiplier=1)
                c2_price = get_op_price(c2, type='midprice', multiplier=1)
                p1_price = get_op_price(p1, type='midprice', multiplier=1)
                p2_price = get_op_price(p2, type='midprice', multiplier=1)

                c1_delta = compute_delta(op_info, c1)
                c2_delta = compute_delta(op_info, c2)
                p1_delta = compute_delta(op_info, p1)
                p2_delta = compute_delta(op_info, p2)

                etf50_curr_perc =  int(rd115.get(f"KZ:S510050:LATEST"))  / int(rd115.get(f"KZ:S510050:PRECLOSE")) - 1
                etf300_curr_perc = int(rd115.get(f"KZ:S510300:LATEST"))  / int(rd115.get(f"KZ:S510300:PRECLOSE")) - 1
                etf500_curr_perc = int(rd115.get(f"KZ:S510500:LATEST"))  / int(rd115.get(f"KZ:S510500:PRECLOSE")) - 1
                etf915_curr_perc = int(rd115.get(f"KZ:S159915:LATEST"))  / int(rd115.get(f"KZ:S159915:PRECLOSE")) - 1
                etfk50_curr_perc = int(rd115.get(f"KZ:S588000:LATEST"))  / int(rd115.get(f"KZ:S588000:PRECLOSE")) - 1
                curr_perc = (etf50_curr_perc+etf300_curr_perc+etf500_curr_perc+etf915_curr_perc+etfk50_curr_perc) / 5
                
                if not rebalance_complete:
        
                    old_c1_price = get_op_price(old_c1, type='midprice', multiplier=1)
                    old_c2_price = get_op_price(old_c2, type='midprice', multiplier=1)
                    old_p1_price = get_op_price(old_p1, type='midprice', multiplier=1)
                    old_p2_price = get_op_price(old_p2, type='midprice', multiplier=1)
                    
                    price_dict = {
                        c1: c1_price,
                        c2: c2_price,
                        p1: p1_price,
                        p2: p2_price,     
                        old_c1: old_c1_price,
                        old_c2: old_c2_price,
                        old_p1: old_p1_price,
                        old_p2: old_p2_price, 
                    }
                    holding_asset = 0
                    for c in rebalance_scehdule:
                        holding_asset += (rebalance_scehdule[c][rebalance_counter]*price_dict[c])
                    jz = remaining_cash + holding_asset
            
                    rebalance_counter += 1
        
                    if rebalance_counter > 600:
                        whether_rebalance = False
                        rebalance_complete = True
                        c1, c2, p1, p2 = ed_con_dict['c1'], ed_con_dict['c2'], ed_con_dict['p1'], ed_con_dict['p2']
                        c1_num, c2_num, p1_num, p2_num = rebalance_scehdule[c1][-1], rebalance_scehdule[c2][-1], rebalance_scehdule[p1][-1], rebalance_scehdule[p2][-1]
                        rebalance_counter = 1
                    else:
                        holding_asset = 0
                        slippage = 0
                        for c in rebalance_scehdule:
                            holding_asset += (rebalance_scehdule[c][rebalance_counter]*price_dict[c])
                            slippage += ( abs(rebalance_scehdule[c][rebalance_counter] - rebalance_scehdule[c][rebalance_counter-1])*8 )
                        remaining_cash = jz - holding_asset
        
                        if contract_month == 0:
                            remaining_cash += slippage
                        else:
                            remaining_cash -= slippage
                        
                    print('Rebalancing!', ' time: ', datetime.datetime.now(), ' | jz: ', '{:.1f}'.format(jz))
                    
                else:
        
                    jz = remaining_cash + (c1_price*c1_num + c2_price*c2_num + p1_price*p1_num + p2_price*p2_num)
 
                ################################
                ####### REBALANCE MODULE #######
                ################################

                if abs(curr_perc - rebalance_benchmark) > 0.01 and rebalance_complete:
                    whether_rebalance = True
                    rebalance_benchmark = curr_perc
                
                ########## A5 Checker ##########
                
                curr_a5_lst = [
                    get_a5(op_info, underlying, c1),
                    get_a5(op_info, underlying, c2),
                    get_a5(op_info, underlying, p1),
                    get_a5(op_info, underlying, p2),
                ]
                mean_curr_a5 = np.mean(curr_a5_lst)
                max_curr_a5 = max(curr_a5_lst)
                min_curr_a5 = min(curr_a5_lst)
                if len(a5_checker_lst) < 600:
                    a5_checker = True
                    a5_checker_lst.append(mean_curr_a5)
                else:
                    if len(a5_checker_lst) > 600:
                        a5_checker_lst = a5_checker_lst[-600:]
                    recent_a5_mean = sum(a5_checker_lst) / 600
                    if max_curr_a5 > recent_a5_mean + 30 or min_curr_a5 < recent_a5_mean - 30:
                        a5_checker = False
                        a5_checker_count += 1
                    else:
                        a5_checker = True
                        a5_checker_count = 0
                        a5_checker_lst.append(mean_curr_a5)

                    if a5_checker_count > 200:
                        a5_checker = True
                        a5_checker_count = 0
                        a5_checker_lst = []
                        a5_checker_lst.append(mean_curr_a5)

                ########## Price Checker ##########

                price_checker = (c1_price > 0 and c2_price > 0 and p1_price > 0 and p2_price > 0)

                ########## Meltdown Checker ##########

                meltdown_checker_lst = np.array([
                    get_spread(c1),
                    get_spread(c2),
                    get_spread(p1),
                    get_spread(p2),
                ])
                num_meltdown = (meltdown_checker_lst[meltdown_checker_lst < 1e-8]).shape[0]
                if num_meltdown < 1:
                    meltdown_checker = True
                else:
                    meltdown_checker = False

                ########## Time Checker ##########

                time_checker = curr_time <= rebalance_stop_time

                ################################

                whether_cont_trading = a5_checker and price_checker and meltdown_checker

                if whether_rebalance and whether_cont_trading and rebalance_complete and time_checker:

                    print('########################', curr_time, 'Rebalance Triggered! ########################')

                    rebalance_complete = False 

                    st_dict = {
                        c1: c1_num,
                        c2: c2_num,
                        p1: p1_num,
                        p2: p2_num,
                    }
        
                    price_dict = {
                        c1: c1_price,
                        c2: c2_price,
                        p1: p1_price,
                        p2: p2_price,     
                    }
        
                    old_c1 = c1
                    old_c2 = c2
                    old_p1 = p1
                    old_p2 = p2

                    con_rebalance_condition = c1_delta < 0.3 or p1_delta > -0.3
                
                    if con_rebalance_condition:

                        con_info = get_option_contracts_live(op_info, underlying)
                        c1, c2, p1, p2 = con_info
                        
                    pos_info = get_option_positions_live(op_info, con_info, jz)
                    c1_num, c2_num, p1_num, p2_num = pos_info

                    ed_dict = {
                        c1: c1_num,
                        c2: c2_num,
                        p1: p1_num,
                        p2: p2_num,
                    }
        
                    ed_price_dict = {
                        c1: c1_price,
                        c2: c2_price,
                        p1: p1_price,
                        p2: p2_price,     
                    }
        
                    ed_con_dict = {
                        'c1': c1, 
                        'c2': c2,
                        'p1': p1,
                        'p2': p2,
                    }
        
                    price_dict.update(ed_price_dict)
        
                    rebalance_scehdule = get_smooth_adj_schedule(st_dict, ed_dict)
        
                    holding_asset = 0
                    slippage = 0
                    for c in rebalance_scehdule:
                        holding_asset += (rebalance_scehdule[c][rebalance_counter]*price_dict[c])
                        slippage += ( abs(rebalance_scehdule[c][rebalance_counter] - rebalance_scehdule[c][rebalance_counter-1])*8 )
                    remaining_cash = jz - holding_asset
        
                    if contract_month == 0:
                        remaining_cash += slippage
                    else:
                        remaining_cash -= slippage

                    publish_manifest(vopkey, vopname, redis_dict)

                ################################
                ################################
                ################################
                
                op_coef_dict = get_op_coef(c1, c2, p1, p2, vopkey)

                all_time_value = get_time_value(underlying, op_info, c1) * c1_num + \
                                 get_time_value(underlying, op_info, c2) * c2_num + \
                                 get_time_value(underlying, op_info, p1) * p1_num + \
                                 get_time_value(underlying, op_info, p2) * p2_num

                all_margin_b = (get_margin(underlying, op_info, 'b', c1) * c1_num + \
                                get_margin(underlying, op_info, 'b', c2) * c2_num + \
                                get_margin(underlying, op_info, 'b', p1) * p1_num + \
                                get_margin(underlying, op_info, 'b', p2) * p2_num) / 1e4

                all_margin_s = (get_margin(underlying, op_info, 's', c1) * c1_num + \
                                get_margin(underlying, op_info, 's', c2) * c2_num + \
                                get_margin(underlying, op_info, 's', p1) * p1_num + \
                                get_margin(underlying, op_info, 's', p2) * p2_num) / 1e4

                all_margin = {'b': all_margin_b, 's': all_margin_s}

                redis_dict = {
                    c1: c1_num,
                    c2: c2_num,
                    p1: p1_num,
                    p2: p2_num
                }

                tc_prog = round(abs(curr_perc - rebalance_benchmark)*100 / 0.01, 1)

                rd170.publish(vopkey, str(jz))

                rd170.set(f"{vopkey}:jz", jz)
                rd170.set(f"{vopkey}:num_breaks", str(num_meltdown))
                rd170.set(f"{vopkey}:tc_prog", tc_prog)
                rd170.set(f"{vopkey}:op_coef", str(op_coef_dict))
                rd170.set(f"{vopkey}:all_margin", str(all_margin))
                rd170.set(f"{vopkey}:all_tv", all_time_value)

                curtvtimestr = f'H:{datetime.datetime.now().strftime("%H%M%S")}'
                rd181.hset(curtvtimestr, f'ZQ:VOP:sig:{vopname}', str(jz))

                print('time: ', datetime.datetime.now(), ' | jz: ', '{:.1f}'.format(jz), \
                      ' | call 1: ', c1, '{:.2f}'.format(c1_price), '{:.2f}'.format(c1_num), '{:.2f}'.format(c1_delta), \
                      ' | call 2: ', c2, '{:.2f}'.format(c2_price), '{:.2f}'.format(c2_num), '{:.2f}'.format(c2_delta), \
                      ' | put 1: ', p1, '{:.2f}'.format(p1_price), '{:.2f}'.format(p1_num), '{:.2f}'.format(p1_delta), \
                      ' | put 2: ', p2, '{:.2f}'.format(p2_price), '{:.2f}'.format(p2_num), '{:.2f}'.format(p2_delta), \
                      ' | Rebalance Progress: ', '{:.2f}'.format(tc_prog),'%', ' | Cash: ', '{:.2f}'.format(remaining_cash))

                curr_time_float = time.time()
                next_time = int(curr_time_float + 1)
                sleep_amt = next_time - curr_time_float
                time.sleep(sleep_amt)
            else:
                print(datetime.datetime.now(), 'Waiting for Market Open!')
                curr_time_float = time.time()
                next_time = int(curr_time_float + 1)
                sleep_amt = next_time - curr_time_float
                time.sleep(sleep_amt)

            if curr_time > closing_time:
                
                print('Position Rebalanced:', str(pos_rebalance_count), ' | Contract Rebalanced:', str(con_rebalance_count))

                exdt_status = rd170.hget("DAILY:mdi:opkind:" + underlying, "extoday")
                if exdt_status == '1':
                    rd170.delete(f"{vopkey}:manifest")
                break

        except:
            print('################## FATAL ERROR! ##################')
            traceback.print_exc()
            time.sleep(1)



if __name__ == '__main__':
    from argparse import ArgumentParser
    parser = ArgumentParser('可以添加以下变量')
    parser.add_argument('-O', '--opkind', type=str, default='510300', help='输入opkind，默认是300')
    parser.add_argument('-M', '--month', type=int, default=0, help='输入month，默认是0代表当前月，1代表下个月')
    args = parser.parse_args()
    underlying = args.opkind
    contract_month = args.month
    main(underlying, contract_month)
