from nb_common import *
from tqdm.notebook import tqdm
from joblib import Parallel, delayed
import pickle 
import copy
import os
from pympler import asizeof
import traceback
from ut_redis import FrRedis
import calendar



def reconstruct_orderbook(stock, date, show_progress_bar=True, complete_orderbook=False):

    is_etf = True if stock in ['510050','510300','510310','510330','510500','588000','588080','512100','159915','159919','159922'] else False
    
    if stock[:1] == '6' or stock in ['510050','510300','510310','510330','510500','588000','588080','512100']:

        raw = pd.read_parquet('/A/MD/orderbook/raw/'+str(date)+'/'+str(stock)+'.parquet')

        stock_postfix = '.XSHG'

        if len(raw) < 100:
            return pd.DataFrame()
    
        df = []
        a_time = []
        orderbook = {}
        orderbook_vol = {}
        orderbook_vol_lg = {}
        orderpool = {}
        new_order_dict = {}
        orderpool_sm = set()
        orderpool_mid = set()
        orderpool_lg = set()
        orderpool_slg = set()
        sm_order_cum_vol = 0
        mid_order_cum_vol = 0
        lg_order_cum_vol = 0
        slg_order_cum_vol = 0
        order_aggr_dict = {}
        curr_money = 0
        cum_money = 0
        prev_cum_money = 0
        curr_vol = 0
        cum_vol = 0
        prev_cum_vol = 0
        order_aggr = 1
        mid_price = None
        latest = None
        sorted_price_levels = []
        bid1, ask1 = None, None
        total_buy_qty = 0
        total_sell_qty = 0
        md_1d_file = pd.read_pickle('/A/MD/1d/s/md_s_'+stock+stock_postfix+'.pickle').loc[date]
        upper_limit = int(md_1d_file.high_limit*1000+1e-8)
        lower_limit = int(md_1d_file.low_limit*1000+1e-8)
        prev_close = int(md_1d_file.pre_close*1000+1e-8)
        
        mid_threshold = 50000 / ((lower_limit+upper_limit)*0.001/2)
        lg_threshold = 300000 / ((lower_limit+upper_limit)*0.001/2)
        slg_threshold = 1000000 / ((lower_limit+upper_limit)*0.001/2)
        
        raw_len = len(raw)-1
        whether_snapshot = False
        channel_no = None

        if lower_limit < 1000:
            return pd.DataFrame()
            
        if prev_close > 10000:
            disc_coef = 0.001
        elif prev_close > 5000 and prev_close <= 10000:
            disc_coef = 0.002
        elif prev_close > 2000 and prev_close <= 5000:
            disc_coef = 0.005
        else:
            disc_coef = 0.01

        increment = 10 if not is_etf else 1
                    
        tier_dict = {}
        for p in  range(lower_limit, upper_limit+increment, increment):
            tier_dict[p] = round(round((p/prev_close-1)/disc_coef)*disc_coef*1000)
        orderbook_tier = {p:0 for p in np.sort(list(set(tier_dict.values())))}

        if complete_orderbook:
            col_names = ['orderbook']
        else:
            col_names = []
            col_names += ['bid'+str(i)+'_price' for i in range(1, 20+1)]
            col_names += ['bid'+str(i)+'_qty' for i in range(1, 20+1)]
            col_names += ['ask'+str(i)+'_price' for i in range(1, 20+1)]
            col_names += ['ask'+str(i)+'_qty' for i in range(1, 20+1)]
            col_names += ['bid'+str(i)+'_order_num' for i in range(1, 20+1)]
            col_names += ['ask'+str(i)+'_order_num' for i in range(1, 20+1)]
            col_names += ['bid'+str(i)+'_lg_qty' for i in range(1, 20+1)]
            col_names += ['ask'+str(i)+'_lg_qty' for i in range(1, 20+1)]
            col_names += ['price_tier_'+str(np.abs(i)) if i < 0 else 'price_tier'+str(i) for i in orderbook_tier]
            col_names += ['prev_close', 'latest', 'mid_price', 'spread', \
                          'tick_type', 'tick_vol', 'tick_money', 'tick_direc', \
                          'order_aggr', \
                          'total_buy_qty', 'total_sell_qty', \
                          'sm_order_cum_amt', 'mid_order_cum_amt', 'lg_order_cum_amt', 'slg_order_cum_amt']

        if show_progress_bar:
            iterator = tqdm(range(raw.shape[0]))
        else:
            iterator = range(raw.shape[0])

        common_channel = raw.Channel.value_counts().index[0]
        
        for idx in iterator:

            # date = raw.RecvTime.iloc[0].split()[0] 
            time = date+' '+raw.TickTime.iloc[idx]
            time_dt = datetime.datetime.strptime(time, '%Y-%m-%d %H:%M:%S.%f')
            time_hms = str(time_dt.time())

            if raw.Channel.iloc[idx] != common_channel:
                continue
        
            if (time_hms > '09:29:00.000000' and time_hms < '14:57:00.000000') or \
               idx == raw_len:
                whether_snapshot = True
            else:
                whether_snapshot = False
            
            if time_hms > '09:29:00.000000' and not bid1 and not ask1:
                sorted_price_levels = list(sorted(orderbook.keys()))
                if total_buy_qty == 0:
                    bid1 = sorted_price_levels[0]
                    bid1_index = 0
                    ask1 = sorted_price_levels[0]
                    ask1_index = 0
                elif total_sell_qty == 0:
                    bid1 = sorted_price_levels[-1]
                    bid1_index = len(sorted_price_levels)-1
                    ask1 = sorted_price_levels[-1]
                    ask1_index = len(sorted_price_levels)-1
                else:
                    num_lvs = len(sorted_price_levels)
                    for i in range(num_lvs):
                        bid = sorted_price_levels[max(i-1,0)]
                        ask = sorted_price_levels[max(i,0)]
                        if (len(orderbook[bid][1]) == 0 and \
                            len(orderbook[bid][0]) > 0 and \
                            len(orderbook[ask][0]) == 0 and \
                            len(orderbook[ask][1]) > 0): 
                            bid1 = bid
                            bid1_index = i-1
                            ask1 = ask
                            ask1_index = i
                            break  

                if complete_orderbook:

                    tick_info = []
                    a_time.append(date+' 09:25:00.000000')
                    tick_info += [
                        dict(sorted(copy.deepcopy(orderbook).items())[::-1]),
                    ]
                    df.append(tick_info)

                else:

                    tick_info = []
                    a_time.append(date+' 09:25:00.000000')

                    bid_prices = []
                    bid_qtys = []
                    ask_prices = []
                    ask_qtys = []

                    for i in range(100):
                        try_bid_index = max(bid1_index-i, 0)
                        try_bid_price = sorted_price_levels[try_bid_index]
                        if orderbook_vol[try_bid_price][0] > 0 or (bid1_index-i) <= 0:
                            bid_prices.append(try_bid_price)
                            bid_qtys.append(orderbook_vol[try_bid_price][0])
                        if len(bid_prices) >= 20:
                            break
                    for i in range(100):
                        try_ask_index = min(ask1_index+i, len(sorted_price_levels)-1)
                        try_ask_price = sorted_price_levels[try_ask_index]
                        if orderbook_vol[try_ask_price][1] > 0 or (ask1_index+i) >= len(sorted_price_levels)-1:
                            ask_prices.append(try_ask_price)
                            ask_qtys.append(orderbook_vol[try_ask_price][1])
                        if len(ask_prices) >= 20:
                            break
        
                    bid_num_orders = [len(orderbook[p][0]) for p in bid_prices[:20]]
                    ask_num_orders = [len(orderbook[p][1]) for p in ask_prices[:20]]

                    bid_lg_qtys = [orderbook_vol_lg[p][0] if p in orderbook_vol_lg else 0 for p in bid_prices[:20]]
                    ask_lg_qtys = [orderbook_vol_lg[p][1] if p in orderbook_vol_lg else 0 for p in ask_prices[:20]]

                    price_tier_arr = [orderbook_tier[p] for p in orderbook_tier]

                    tick_info += bid_prices
                    tick_info += bid_qtys
                    tick_info += ask_prices
                    tick_info += ask_qtys
                    tick_info += bid_num_orders
                    tick_info += ask_num_orders
                    tick_info += bid_lg_qtys
                    tick_info += ask_lg_qtys
                    tick_info += price_tier_arr
                    
                    mid_price = (bid1+ask1)/2
                    spread = ask1 - bid1
            
                    if tick_type == 2:
                        curr_money = cum_money - prev_cum_money
                        prev_cum_money = cum_money
                
                        curr_vol = cum_vol - prev_cum_vol
                        prev_cum_vol = cum_vol
            
                    if idx == raw_len:
                        tick_vol = curr_vol
                        tick_money = curr_money
                        tick_direc = 0

                    tick_direc = 0
                    tick_info += [prev_close, latest, mid_price, spread, \
                                  tick_type, curr_vol, curr_money, tick_direc, \
                                  order_aggr, \
                                  total_buy_qty, total_sell_qty, \
                                  sm_order_cum_vol, mid_order_cum_vol, lg_order_cum_vol, slg_order_cum_vol]
                    
                    df.append(tick_info)            
        
            #################################
            ######### Place Order ###########
            #################################
        
            
            data_type = raw.Type.iloc[idx]
            
            if data_type == 'A':
                
                if raw.TickBSFlag.iloc[idx] == 'B':
                    direction = 1
                elif raw.TickBSFlag.iloc[idx] == 'S':
                    direction = 2
                
                order_direc = direction
                order_qty = raw.Qty.iloc[idx]
                order_no = raw.BuyOrderNO.iloc[idx] if direction == 1 else raw.SellOrderNO.iloc[idx] 
                if order_no in new_order_dict:
                    order_qty_ = order_qty + new_order_dict[order_no][0]
                    new_order_dict[order_no][0] += order_qty
                else:
                    order_qty_ = 0

                order_price = int(round(raw.Price.iloc[idx]*1000))
                
                orderpool[order_no] = [order_price, order_qty]
                order_money = (order_price/1000)*order_qty
        
                if bid1 and ask1:
                    if order_no in order_aggr_dict:
                        order_aggr = order_price / order_aggr_dict[order_no]
                    else:
                        order_aggr = order_price / ((bid1+ask1)/2)
                
                if order_price not in orderbook: 
                    orderbook[order_price] = [{},{}]
                    orderbook[order_price][direction-1][order_no] = order_qty
                else:
                    orderbook[order_price][direction-1][order_no] = order_qty
        
                if order_price not in orderbook_vol:
                    orderbook_vol[order_price] = [0, 0]
                orderbook_vol[order_price][direction-1] += order_qty
        
                if max(order_qty, order_qty_) <= mid_threshold:
                    orderpool_sm.add(order_no)
                elif max(order_qty, order_qty_) > mid_threshold and max(order_qty, order_qty_) <= lg_threshold:
                    orderpool_mid.add(order_no)
                elif max(order_qty, order_qty_) > lg_threshold and max(order_qty, order_qty_) <= slg_threshold:
                    orderpool_lg.add(order_no)
                elif max(order_qty, order_qty_) > slg_threshold:
                    orderpool_slg.add(order_no)
        
                if max(order_qty, order_qty_) > lg_threshold:
                    if order_price not in orderbook_vol_lg:
                        orderbook_vol_lg[order_price] = [0, 0]
                    orderbook_vol_lg[order_price][direction-1] += order_qty
        
                if bid1 and ask1 and whether_snapshot:
        
                    if len(sorted_price_levels) == 0 or len(sorted_price_levels) != len(orderbook):
                        sorted_price_levels = list(sorted(orderbook.keys()))
                    
                    if direction == 1:
                        if (order_price > bid1 and order_price < ask1) or \
                           (total_buy_qty == 0 and order_price < ask1) or \
                           (total_sell_qty == 0 and order_price > bid1):
                            bid1 = order_price
                    else:
                        if (order_price > bid1 and order_price < ask1) or \
                           (total_buy_qty == 0 and order_price < ask1) or \
                           (total_sell_qty == 0 and order_price > bid1):
                            ask1 = order_price      
                        
                if direction == 1:
                    total_buy_qty += order_qty
                else:
                    total_sell_qty += order_qty
                            
                tick_type = 0
                tick_vol = order_qty 
                tick_money = (order_price/1000)*order_qty
                tick_direc = direction 

                target_tier = tier_dict[order_price]
                orderbook_tier[target_tier] += order_qty
               
        
            #################################
            ######### Cancel Order ##########
            #################################
        
            
            elif data_type == 'D':
                
                cancel_qty = raw.Qty.iloc[idx]
                
                if raw.TickBSFlag.iloc[idx] == 'B':
                    direction = 1
                elif raw.TickBSFlag.iloc[idx] == 'S':
                    direction = 2
                cancel_direc = direction
                trade_direc = 0
                order_direc = 0
                whether_trade = 0
                cancel_index = raw.BuyOrderNO.iloc[idx] if direction == 1 else raw.SellOrderNO.iloc[idx] 
                cancel_price = int(round(raw.Price.iloc[idx]*1000))
                
                if bid1 and ask1 and whether_snapshot:
            
                    if bid1 == cancel_price and cancel_qty == orderbook_vol[cancel_price][0]:
                        bid1_index = sorted_price_levels.index(bid1)
                        bid2 = sorted_price_levels[max(bid1_index-1,0)]
                        bid1 = bid2
                    elif ask1 == cancel_price and cancel_qty == orderbook_vol[cancel_price][1]:
                        ask1_index = sorted_price_levels.index(ask1)
                        ask2 = sorted_price_levels[min(ask1_index+1,len(sorted_price_levels)-1)]
                        ask1 = ask2
                
                orderbook[cancel_price][direction-1].pop(cancel_index)
                orderpool.pop(cancel_index)
                if len(orderbook[cancel_price][0]) == len(orderbook[cancel_price][1]) == 0:
                    orderbook.pop(cancel_price)
        
                if direction == 1:
                    total_buy_qty -= cancel_qty
                else:
                    total_sell_qty -= cancel_qty
        
                orderbook_vol[cancel_price][direction-1] -= cancel_qty
                if orderbook_vol[cancel_price][0] == 0 and \
                   orderbook_vol[cancel_price][1] == 0:
                    orderbook_vol.pop(cancel_price)
        
                if cancel_index in orderpool_lg:
                    orderbook_vol_lg[cancel_price][direction-1] -= cancel_qty
                    if orderbook_vol_lg[cancel_price][0] == 0 and \
                       orderbook_vol_lg[cancel_price][1] == 0:
                        orderbook_vol_lg.pop(cancel_price)
        
                if bid1 and ask1 and whether_snapshot:
                    if len(sorted_price_levels) == 0 or len(sorted_price_levels) != len(orderbook):
                        sorted_price_levels = list(sorted(orderbook.keys()))
        
                tick_type = 1
                tick_vol = cancel_qty 
                tick_money = cancel_qty * (cancel_price/1000)
                tick_direc = direction 
        
                order_aggr = 1

                target_tier = tier_dict[cancel_price]
                orderbook_tier[target_tier] -= cancel_qty
        
            
            #################################
            ######### Transaction ###########
            #################################
                    
        
            elif data_type == 'T':
                trade_qty = raw.Qty.iloc[idx]
                direction = 1 if raw.TickBSFlag.iloc[idx] == 'B' else 2
                trade_direc = direction
    
                order_direc = 0
                cancel_direc = 0
                trade_price = int(round(raw.Price.iloc[idx]*1000))
                latest = trade_price
                whether_trade = 1
                trade_money = raw.TradeMoney.iloc[idx]
                buy_order = raw.BuyOrderNO.iloc[idx] 
                sell_order = raw.SellOrderNO.iloc[idx] 
        
                if direction == '' or (buy_order in orderpool and sell_order in orderpool):
        
                    if buy_order in orderpool_sm:
                        sm_order_cum_vol += trade_money
                    if sell_order in orderpool_sm:
                        sm_order_cum_vol -= trade_money
            
                    if buy_order in orderpool_mid:
                        mid_order_cum_vol += trade_money
                    if sell_order in orderpool_mid:
                        mid_order_cum_vol -= trade_money
            
                    if buy_order in orderpool_lg:
                        lg_order_cum_vol += trade_money
                    if sell_order in orderpool_lg:
                        lg_order_cum_vol -= trade_money
        
                    if buy_order in orderpool_slg:
                        slg_order_cum_vol += trade_money
                    if sell_order in orderpool_slg:
                        slg_order_cum_vol -= trade_money
            
                    buy_order_price, buy_order_qty = orderpool[buy_order]
                    sell_order_price, sell_order_qty = orderpool[sell_order]
                        
                    if buy_order in orderpool:
                        orderpool[buy_order][1] -= trade_qty
                        if orderpool[buy_order][1] < 0.1:
                            orderpool.pop(buy_order)
                    if sell_order in orderpool:
                        orderpool[sell_order][1] -= trade_qty
                        if orderpool[sell_order][1] < 0.1:
                            orderpool.pop(sell_order)
               
                    orderbook[buy_order_price][0][buy_order] -= trade_qty
                    orderbook[sell_order_price][1][sell_order] -= trade_qty
                    if orderbook[buy_order_price][0][buy_order] < 0.1:
                        orderbook[buy_order_price][0].pop(buy_order)
                    if orderbook[sell_order_price][1][sell_order] < 0.1:
                        orderbook[sell_order_price][1].pop(sell_order)
            
                    if len(orderbook[buy_order_price][0]) == len(orderbook[buy_order_price][1]) == 0:
                        orderbook.pop(buy_order_price)
                    if sell_order_price in orderbook:
                        if len(orderbook[sell_order_price][0]) == len(orderbook[sell_order_price][1]) == 0:
                            orderbook.pop(sell_order_price)
            
                    total_buy_qty -= trade_qty
                    total_sell_qty -= trade_qty
            
                    orderbook_vol[buy_order_price][0] -= trade_qty
                    orderbook_vol[sell_order_price][1] -= trade_qty
            
                    if orderbook_vol[buy_order_price][0] == 0 and \
                       orderbook_vol[buy_order_price][1] == 0:
                        orderbook_vol.pop(buy_order_price)
                    if sell_order_price in orderbook_vol:
                        if orderbook_vol[sell_order_price][0] == 0 and \
                           orderbook_vol[sell_order_price][1] == 0:
                            orderbook_vol.pop(sell_order_price)
            
                    if buy_order in orderpool_lg:
                        orderbook_vol_lg[buy_order_price][0] -= trade_qty
                        if orderbook_vol_lg[buy_order_price][0] == 0 and \
                           orderbook_vol_lg[buy_order_price][1] == 0:
                            orderbook_vol_lg.pop(buy_order_price)
            
                    if sell_order in orderpool_lg:
                        orderbook_vol_lg[sell_order_price][1] -= trade_qty
                        if orderbook_vol_lg[sell_order_price][0] == 0 and \
                           orderbook_vol_lg[sell_order_price][1] == 0:
                            orderbook_vol_lg.pop(sell_order_price)

                    target_tier = tier_dict[buy_order_price]
                    orderbook_tier[target_tier] -= trade_qty
                    target_tier = tier_dict[sell_order_price]
                    orderbook_tier[target_tier] -= trade_qty
        
                elif buy_order in orderpool and sell_order not in orderpool:
                
                    if sell_order not in new_order_dict:
                        new_order_dict[sell_order] = [0, 0, direction, time_dt, {'s':0,'m':0,'l':0,'sl':0}]
                    new_order_dict[sell_order][0] += trade_qty
                    new_order_dict[sell_order][1] += trade_money
                    if buy_order in orderpool_sm:
                        new_order_dict[sell_order][4]['s'] += trade_money
                    if buy_order in orderpool_mid:
                        new_order_dict[sell_order][4]['m'] += trade_money
                    if buy_order in orderpool_lg:
                        new_order_dict[sell_order][4]['l'] += trade_money
                    if buy_order in orderpool_slg:
                        new_order_dict[sell_order][4]['sl'] += trade_money
                    
                    if bid1 and ask1:
                        order_aggr_dict[sell_order] = int((bid1+ask1)/2)
        
                    buy_order_price, buy_order_qty = orderpool[buy_order]
        
                    orderpool[buy_order][1] -= trade_qty
                    if orderpool[buy_order][1] < 0.1:
                        orderpool.pop(buy_order)
        
                    orderbook[buy_order_price][0][buy_order] -= trade_qty
                    if orderbook[buy_order_price][0][buy_order] < 0.1:
                        orderbook[buy_order_price][0].pop(buy_order)
            
                    if len(orderbook[buy_order_price][0]) == len(orderbook[buy_order_price][1]) == 0:
                        orderbook.pop(buy_order_price)
              
                    total_buy_qty -= trade_qty
                    
                    orderbook_vol[buy_order_price][0] -= trade_qty
            
                    if orderbook_vol[buy_order_price][0] == 0 and \
                       orderbook_vol[buy_order_price][1] == 0:
                        orderbook_vol.pop(buy_order_price)
                        
                    if buy_order in orderpool_lg:
                        orderbook_vol_lg[buy_order_price][0] -= trade_qty
                        if orderbook_vol_lg[buy_order_price][0] == 0 and \
                           orderbook_vol_lg[buy_order_price][1] == 0:
                            orderbook_vol_lg.pop(buy_order_price)

                    target_tier = tier_dict[buy_order_price]
                    orderbook_tier[target_tier] -= trade_qty
        
                elif buy_order not in orderpool and sell_order in orderpool:
                        
                    if buy_order not in new_order_dict:
                        new_order_dict[buy_order] = [0, 0, direction, time_dt, {'s':0,'m':0,'l':0,'sl':0}]
                    new_order_dict[buy_order][0] += trade_qty
                    new_order_dict[buy_order][1] += trade_money
                    if sell_order in orderpool_sm:
                        new_order_dict[buy_order][4]['s'] += trade_money   
                    if sell_order in orderpool_mid:
                        new_order_dict[buy_order][4]['m'] += trade_money   
                    if sell_order in orderpool_lg:
                        new_order_dict[buy_order][4]['l'] += trade_money 
                    if sell_order in orderpool_slg:
                        new_order_dict[buy_order][4]['sl'] += trade_money 
        
                    if bid1 and ask1:
                        order_aggr_dict[buy_order] = int((bid1+ask1)/2)
        
                    sell_order_price, sell_order_qty = orderpool[sell_order]
            
                    orderpool[sell_order][1] -= trade_qty
                    if orderpool[sell_order][1] < 0.1:
                        orderpool.pop(sell_order)
               
                    orderbook[sell_order_price][1][sell_order] -= trade_qty
                    if orderbook[sell_order_price][1][sell_order] < 0.1:
                        orderbook[sell_order_price][1].pop(sell_order)    
                    if len(orderbook[sell_order_price][0]) == len(orderbook[sell_order_price][1]) == 0:
                        orderbook.pop(sell_order_price)
            
                    total_sell_qty -= trade_qty
                    
                    orderbook_vol[sell_order_price][1] -= trade_qty
            
                    if orderbook_vol[sell_order_price][0] == 0 and \
                       orderbook_vol[sell_order_price][1] == 0:
                        orderbook_vol.pop(sell_order_price)
            
                    if sell_order in orderpool_lg:
                        orderbook_vol_lg[sell_order_price][1] -= trade_qty
                        if orderbook_vol_lg[sell_order_price][0] == 0 and \
                           orderbook_vol_lg[sell_order_price][1] == 0:
                            orderbook_vol_lg.pop(sell_order_price)

                    target_tier = tier_dict[sell_order_price]
                    orderbook_tier[target_tier] -= trade_qty
             
                if bid1 and ask1 and whether_snapshot:
                    
                    if len(sorted_price_levels) == 0 or len(sorted_price_levels) != len(orderbook):
                        sorted_price_levels = list(sorted(orderbook.keys()))
            
                    if total_buy_qty == 0:
                        bid1 = sorted_price_levels[0]
                        ask1 = sorted_price_levels[0]
                    elif total_sell_qty == 0:
                        bid1 = sorted_price_levels[-1] 
                        ask1 = sorted_price_levels[-1]
                    else:
                        for price in range(latest, upper_limit+increment, increment):
                            if price in orderbook_vol:
                                if orderbook_vol[price][0] == 0 and orderbook_vol[price][1] > 0:
                                    ask1 = price
                                    break
                        for price in range(latest, lower_limit-increment, -increment):
                            if price in orderbook_vol:
                                if orderbook_vol[price][0] > 0 and orderbook_vol[price][1] == 0:
                                    bid1 = price
                                    break
                                        
                cum_money += trade_money     
                cum_vol += trade_qty
                
                tick_type = 2
                tick_vol = trade_qty 
                tick_money = trade_money
                tick_direc = direction 
                
                order_aggr = 1
        
            to_del = []
            for order in new_order_dict:
                vol, mony, direc, trigger_time, rival_order_dict = new_order_dict[order]
                if (time_dt-trigger_time).total_seconds() > 1:
                    to_del.append(order)
                    if direc == 1:
                        sm_order_cum_vol -= rival_order_dict['s']
                        mid_order_cum_vol -= rival_order_dict['m']
                        lg_order_cum_vol -= rival_order_dict['l']
                        slg_order_cum_vol -= rival_order_dict['sl']
                        if vol <= mid_threshold:
                            sm_order_cum_vol += mony
                        elif vol > mid_threshold and vol <= lg_threshold:
                            mid_order_cum_vol += mony
                        elif vol > lg_threshold and vol <= slg_threshold:
                            lg_order_cum_vol += mony
                        elif vol > slg_threshold:
                            slg_order_cum_vol += mony
                    elif direc == 2:
                        sm_order_cum_vol += rival_order_dict['s']
                        mid_order_cum_vol += rival_order_dict['m']
                        lg_order_cum_vol += rival_order_dict['l']
                        slg_order_cum_vol += rival_order_dict['sl']
                        if vol <= mid_threshold:
                            sm_order_cum_vol -= mony
                        elif vol > mid_threshold and vol <= lg_threshold:
                            mid_order_cum_vol -= mony
                        elif vol > lg_threshold and vol <= slg_threshold:
                            lg_order_cum_vol -= mony
                        elif vol > slg_threshold:
                            slg_order_cum_vol -= mony
            for order in to_del:
                new_order_dict.pop(order)
                                
            if idx == raw_len:
                sorted_price_levels = list(sorted(orderbook.keys()))
                if total_buy_qty == 0:
                    bid1 = sorted_price_levels[0]
                    bid1_index = 0
                    ask1 = sorted_price_levels[0]
                    ask1_index = 0
                elif total_sell_qty == 0:
                    bid1 = sorted_price_levels[-1]
                    bid1_index = len(sorted_price_levels)-1
                    ask1 = sorted_price_levels[-1]
                    ask1_index = len(sorted_price_levels)-1
                else:
                    num_lvs = len(sorted_price_levels)
                    for i in range(num_lvs):
                        bid = sorted_price_levels[max(i-1,0)]
                        ask = sorted_price_levels[max(i,0)]
                        if (len(orderbook[bid][1]) == 0 and \
                            len(orderbook[bid][0]) > 0 and \
                            len(orderbook[ask][0]) == 0 and \
                            len(orderbook[ask][1]) > 0): 
                            bid1 = bid
                            bid1_index = i-1
                            ask1 = ask
                            ask1_index = i
                            break   
        
            if whether_snapshot:
                
                max_lv = len(sorted_price_levels)-1
                if total_buy_qty == 0:
                    bid1_index = 0
                    ask1_index = 0
                elif total_sell_qty == 0:
                    bid1_index = max_lv
                    ask1_index = max_lv
                else:
                    bid1_index = sorted_price_levels.index(bid1)
                    ask1_index = min(bid1_index+1, max_lv)

                if complete_orderbook:

                    a_time.append(time)
                    tick_info = []
                    tick_info += [
                        dict(sorted(copy.deepcopy(orderbook).items())[::-1]),
                    ]
                    df.append(tick_info)

                else:

                    tick_info = []
                    a_time.append(time)

                    bid_prices = []
                    bid_qtys = []
                    ask_prices = []
                    ask_qtys = []

                    for i in range(100):
                        try_bid_index = max(bid1_index-i, 0)
                        try_bid_price = sorted_price_levels[try_bid_index]
                        if orderbook_vol[try_bid_price][0] > 0 or (bid1_index-i) <= 0:
                            bid_prices.append(try_bid_price)
                            bid_qtys.append(orderbook_vol[try_bid_price][0])
                        if len(bid_prices) >= 20:
                            break
                       
                    for i in range(100):
                        try_ask_index = min(ask1_index+i, len(sorted_price_levels)-1)
                        try_ask_price = sorted_price_levels[try_ask_index]
                        if orderbook_vol[try_ask_price][1] > 0 or (ask1_index+i) >= len(sorted_price_levels)-1:
                            ask_prices.append(try_ask_price)
                            ask_qtys.append(orderbook_vol[try_ask_price][1])
                        if len(ask_prices) >= 20:
                            break

                    bid_num_orders = [len(orderbook[p][0]) for p in bid_prices[:20]]
                    ask_num_orders = [len(orderbook[p][1]) for p in ask_prices[:20]]

                    bid_lg_qtys = [orderbook_vol_lg[p][0] if p in orderbook_vol_lg else 0 for p in bid_prices[:20]]
                    ask_lg_qtys = [orderbook_vol_lg[p][1] if p in orderbook_vol_lg else 0 for p in ask_prices[:20]]

                    price_tier_arr = [orderbook_tier[p] for p in orderbook_tier]

                    tick_info += bid_prices
                    tick_info += bid_qtys
                    tick_info += ask_prices
                    tick_info += ask_qtys
                    tick_info += bid_num_orders
                    tick_info += ask_num_orders
                    tick_info += bid_lg_qtys
                    tick_info += ask_lg_qtys
                    tick_info += price_tier_arr
                    
                    mid_price = (bid1+ask1)/2
                    spread = ask1 - bid1
            
                    if tick_type == 2:
                        curr_money = cum_money - prev_cum_money
                        prev_cum_money = cum_money
                
                        curr_vol = cum_vol - prev_cum_vol
                        prev_cum_vol = cum_vol
            
                    if idx == raw_len:
                        tick_vol = curr_vol
                        tick_money = curr_money
                        tick_direc = 0

                    tick_info += [prev_close, latest, mid_price, spread, \
                                  tick_type, tick_vol, tick_money, tick_direc, \
                                  order_aggr, \
                                  total_buy_qty, total_sell_qty, \
                                  sm_order_cum_vol, mid_order_cum_vol, lg_order_cum_vol, slg_order_cum_vol]
            
                    df.append(tick_info)          
            
        orderbook = {key: orderbook[key] for key in sorted(orderbook.keys())[::-1]} 
        orderbook_vol = {key: orderbook_vol[key] for key in sorted(orderbook_vol.keys())[::-1]} 
        orderbook_vol_lg = {key: orderbook_vol_lg[key] for key in sorted(orderbook_vol_lg.keys())[::-1]} 

        orderbook_tier = {key: orderbook_tier[key] for key in sorted(orderbook_tier.keys())[::-1]} 
            
        a_time[-1 ] = date+' 15:00:00.000000'

        df = pd.DataFrame(np.array(df))
        df.columns = col_names   
        df.index = pd.to_datetime(a_time)
    
        # return df, orderbook_vol, orderbook_tier
        return df

    else:

        raw = pd.read_parquet('/A/MD/orderbook/raw/'+str(date)+'/'+str(stock)+'.parquet')

        stock_postfix = '.XSHE'

        if len(raw) < 100 or len(raw) > 1500000:
            return pd.DataFrame()
        
        df = []
        a_time = []
        orderbook = {}
        orderbook_vol = {}
        orderbook_vol_lg = {}
        unmatched_order = {}
        orderpool = {}
        orderpool_sm = set()
        orderpool_mid = set()
        orderpool_lg = set()
        orderpool_slg = set()
        sm_order_cum_vol = 0
        mid_order_cum_vol = 0
        lg_order_cum_vol = 0
        slg_order_cum_vol = 0
        order_aggr = 1
        latest = None
        curr_mid_price = None
        whether_snapshot = False
        sorted_price_levels = []
        bid1, ask1 = None, None
        total_buy_qty = 0
        total_sell_qty = 0
        md_1d_file = pd.read_pickle('/A/MD/1d/s/md_s_'+stock+stock_postfix+'.pickle').loc[date]
        upper_limit = int(md_1d_file.high_limit*1000+1e-8)
        lower_limit = int(md_1d_file.low_limit*1000+1e-8)
        prev_close = int(md_1d_file.pre_close*1000+1e-8)
        mid_threshold = 50000 / ((lower_limit+upper_limit)*0.001/2)
        lg_threshold = 300000 / ((lower_limit+upper_limit)*0.001/2)
        slg_threshold = 1000000 / ((lower_limit+upper_limit)*0.001/2)
        
        raw_len = len(raw)-1
        curr_money = 0
        cum_money = 0
        prev_cum_money = 0
        curr_vol = 0
        cum_vol = 0
        prev_cum_vol = 0
        
        mid_prices = []

        if lower_limit < 1000:
            return pd.DataFrame()

        if prev_close > 10000:
            disc_coef = 0.001
        elif prev_close > 5000 and prev_close <= 10000:
            disc_coef = 0.002
        elif prev_close > 2000 and prev_close <= 5000:
            disc_coef = 0.005
        else:
            disc_coef = 0.01

        increment = 10 if not is_etf else 1
        
        tier_dict = {}
        for p in  range(lower_limit, upper_limit+increment, increment):
            tier_dict[p] = round(round((p/prev_close-1)/disc_coef)*disc_coef*1000)
        orderbook_tier = {p:0 for p in np.sort(list(set(tier_dict.values())))}

        if complete_orderbook:
            col_names = ['orderbook']
        else:
        
            col_names = []
            col_names += ['bid'+str(i)+'_price' for i in range(1, 10+1)]
            col_names += ['bid'+str(i)+'_qty' for i in range(1, 10+1)]
            col_names += ['ask'+str(i)+'_price' for i in range(1, 10+1)]
            col_names += ['ask'+str(i)+'_qty' for i in range(1, 10+1)]
            col_names += ['bid'+str(i)+'_order_num' for i in range(1, 10+1)]
            col_names += ['ask'+str(i)+'_order_num' for i in range(1, 10+1)]
            col_names += ['bid'+str(i)+'_lg_qty' for i in range(1, 10+1)]
            col_names += ['ask'+str(i)+'_lg_qty' for i in range(1, 10+1)]
            col_names += ['price_tier_'+str(np.abs(i)) if i < 0 else 'price_tier'+str(i) for i in orderbook_tier]
            col_names += ['prev_close','latest', 'mid_price', 'spread', \
                          'tick_type', 'tick_vol', 'tick_money', 'tick_direc', \
                          'order_aggr', \
                          'total_buy_qty', 'total_sell_qty', \
                          'sm_order_cum_amt', 'mid_order_cum_amt', 'lg_order_cum_amt', 'slg_order_cum_amt']

        if show_progress_bar:
            iterator = tqdm(range(raw.shape[0]))
        else:
            iterator = range(raw.shape[0])
        
        for idx in iterator:

            # date = raw.RecvTime.iloc[0].split()[0]
            time = date+' '+raw.TransactTime.iloc[idx]
            time_dt = datetime.datetime.strptime(time, '%Y-%m-%d %H:%M:%S.%f')
            time_hms = str(time_dt.time())
            
            if (time_hms > '09:25:00.000000' and time_hms < '14:57:00.000000') or \
               idx == raw_len:
                whether_snapshot = True
            else:
                whether_snapshot = False
            
            if time_hms > '09:25:00.000000' and not bid1 and not ask1:
                sorted_price_levels = list(sorted(orderbook.keys()))
                if total_buy_qty == 0:
                    bid1 = sorted_price_levels[0]
                    bid1_index = 0
                    ask1 = sorted_price_levels[0]
                    ask1_index = 0
                elif total_sell_qty == 0:
                    bid1 = sorted_price_levels[-1]
                    bid1_index = len(sorted_price_levels)-1 
                    ask1 = sorted_price_levels[-1]
                    ask1_index = len(sorted_price_levels)-1
                else:
                    num_lvs = len(sorted_price_levels)
                    for i in range(num_lvs):
                        bid = sorted_price_levels[max(i-1,0)]
                        ask = sorted_price_levels[max(i,0)]
                        if (len(orderbook[bid][1]) == 0 and \
                            len(orderbook[bid][0]) > 0 and \
                            len(orderbook[ask][0]) == 0 and \
                            len(orderbook[ask][1]) > 0): 
                            bid1 = bid
                            bid1_index = i-1
                            ask1 = ask
                            ask1_index = i
                            break  

                if complete_orderbook:

                    a_time.append(date+' 09:25:00.000000')
                    tick_info = []
                    tick_info += [dict(sorted(copy.deepcopy(orderbook).items())[::-1])]

                    df.append(tick_info)
                    
                else:
                
                    bid1_price = bid1
                    if bid1_price in orderbook_vol:
                        bid1_index = sorted_price_levels.index(bid1_price)
                
                    ask1_price = ask1
                    if ask1_price in orderbook_vol:
                        ask1_index = sorted_price_levels.index(ask1_price)
            
                    mid_price = (bid1+ask1)/2
                    spread = ask1 - bid1
                    mid_prices.append(mid_price)
                    bid_prices = []
                    bid_qtys = []
                    ask_prices = []
                    ask_qtys = []
                    
                    for i in range(100):
                        try_bid_index = max(bid1_index-i, 0)
                        try_bid_price = sorted_price_levels[try_bid_index]
                        if orderbook_vol[try_bid_price][0] > 0 or (bid1_index-i) <= 0:
                            bid_prices.append(try_bid_price)
                            bid_qtys.append(orderbook_vol[try_bid_price][0])
                        if len(bid_prices) >= 10:
                            break
                    for i in range(100):
                        try_ask_index = min(ask1_index+i, len(sorted_price_levels)-1)
                        try_ask_price = sorted_price_levels[try_ask_index]
                        if orderbook_vol[try_ask_price][1] > 0 or (ask1_index+i) >= len(sorted_price_levels)-1:
                            ask_prices.append(try_ask_price)
                            ask_qtys.append(orderbook_vol[try_ask_price][1])
                        if len(ask_prices) >= 10:
                            break

                    bid_num_orders = [len(orderbook[p][0]) for p in bid_prices[:10]]
                    ask_num_orders = [len(orderbook[p][1]) for p in ask_prices[:10]]

                    bid_lg_qtys = [orderbook_vol_lg[p][0] if p in orderbook_vol_lg else 0 for p in bid_prices[:10]]
                    ask_lg_qtys = [orderbook_vol_lg[p][1] if p in orderbook_vol_lg else 0 for p in ask_prices[:10]]

                    price_tier_arr = [orderbook_tier[p] for p in orderbook_tier]

                    a_time.append(date+' 09:25:00.000000')
                    tick_info = []
                    
                    tick_info += bid_prices
                    tick_info += bid_qtys
                    tick_info += ask_prices
                    tick_info += ask_qtys
                    tick_info += bid_num_orders
                    tick_info += ask_num_orders
                    tick_info += bid_lg_qtys
                    tick_info += ask_lg_qtys
                    tick_info += price_tier_arr
                    
                    mid_price = (bid1+ask1)/2
                    spread = ask1 - bid1
            
                    curr_money = cum_money - prev_cum_money
                    prev_cum_money = cum_money
            
                    curr_vol = cum_vol - prev_cum_vol
                    prev_cum_vol = cum_vol

                    tick_direc = 0
                    tick_info += [prev_close, latest, mid_price, spread, \
                                  tick_type, curr_vol, curr_money, tick_direc, \
                                  order_aggr, \
                                  total_buy_qty, total_sell_qty, \
                                  sm_order_cum_vol, mid_order_cum_vol, lg_order_cum_vol, slg_order_cum_vol]
                    
                    df.append(tick_info)            
                    

            #################################
            ######### Place Order ###########
            #################################
            
            if raw.ExecType.iloc[idx] == 70:
                data_type = 'transaction'
            elif raw.ExecType.iloc[idx] == 52:
                data_type = 'cancel'
            else:
                data_type = 'order'
            
            if data_type == 'order':
                direction = 1 if raw.Side.iloc[idx] == 49 else 2
                order_qty = int(raw.OrderQty.iloc[idx])
                order_index = int(raw.ApplSeqNum.iloc[idx])

                order_type = raw.OrdType.iloc[idx]
                if order_type == 49:
                    order_price = ask1 if direction == 1 else bid1
                elif order_type == 50:
                    order_price = int(round(raw.Price.iloc[idx]*1000))
                elif order_type == 85:
                    order_price = bid1 if direction == 1 else ask1
        
                if bid1 and ask1 and whether_snapshot:
                    order_aggr = order_price/((bid1+ask1)*0.5)
                  
                orderpool[order_index] = [order_price, order_qty, direction]
        
                if order_price not in orderbook: 
                    orderbook[order_price] = [{},{}]
                    orderbook[order_price][direction-1][order_index] = order_qty
                else:
                    orderbook[order_price][direction-1][order_index] = order_qty
        
                if order_price not in orderbook_vol:
                    orderbook_vol[order_price] = [0, 0]
                orderbook_vol[order_price][direction-1] += order_qty
        
        
                if order_qty <= mid_threshold:
                    orderpool_sm.add(order_index)
                elif order_qty > mid_threshold and order_qty <= lg_threshold:
                    orderpool_mid.add(order_index)
                elif order_qty > lg_threshold and order_qty <= slg_threshold:
                    orderpool_lg.add(order_index)
                elif order_qty > slg_threshold:
                    orderpool_slg.add(order_index)
        
                if order_qty > lg_threshold:
                    if order_price not in orderbook_vol_lg:
                        orderbook_vol_lg[order_price] = [0, 0]
                    orderbook_vol_lg[order_price][direction-1] += order_qty
                        
        
                if bid1 and ask1 and whether_snapshot:
        
                    if direction == 1:
                        if (order_price > bid1 and order_price < ask1) or \
                           (total_buy_qty == 0 and order_price < ask1) or \
                           (total_sell_qty == 0 and order_price > bid1):
                            bid1 = order_price
                    else:
                        if (order_price > bid1 and order_price < ask1) or \
                           (total_buy_qty == 0 and order_price < ask1) or \
                           (total_sell_qty == 0 and order_price > bid1):
                            ask1 = order_price      
                            
                    if len(sorted_price_levels) == 0 or len(sorted_price_levels) != len(orderbook) :
                        sorted_price_levels = list(sorted(orderbook.keys()))
        
                if direction == 1:
                    total_buy_qty += order_qty
                else:
                    total_sell_qty += order_qty
                
                tick_type = 0
                tick_vol = order_qty 
                tick_money = (order_price/1000)*order_qty
                tick_direc = direction 

                target_tier = tier_dict[order_price]
                orderbook_tier[target_tier] += order_qty
        
        
            #################################
            ######### Cancel Order ##########
            #################################
            
            
            elif data_type == 'cancel':
                trade_qty = int(raw.LastQty.iloc[idx])

                order_aggr = 1
                
                cancel_index = int(max(raw.BidApplSeqNum.iloc[idx], raw.OfferApplSeqNum.iloc[idx]))
                
                cancel_price, _, direction = orderpool[cancel_index]
                
                if bid1 and ask1 and whether_snapshot:
                    if bid1 == cancel_price and trade_qty == orderbook_vol[cancel_price][0]:
                        for price in range(bid1-increment, lower_limit-increment, -increment):
                            if price in orderbook_vol:
                                if orderbook_vol[price][0] > 0:
                                    bid1 = price
                                    break
                    elif ask1 == cancel_price and trade_qty == orderbook_vol[cancel_price][1]:
                        for price in range(ask1+increment, upper_limit+increment, increment):
                            if price in orderbook_vol:
                                if orderbook_vol[price][1] > 0:
                                    ask1 = price
                                    break
        
                orderbook[cancel_price][direction-1].pop(cancel_index)
                orderpool.pop(cancel_index)
                if len(orderbook[cancel_price][0]) == len(orderbook[cancel_price][1]) == 0:
                    orderbook.pop(cancel_price)
        
                if direction == 1:
                    total_buy_qty -= trade_qty
                else:
                    total_sell_qty -= trade_qty
        
                orderbook_vol[cancel_price][direction-1] -= trade_qty
                if orderbook_vol[cancel_price][0] == 0 and \
                   orderbook_vol[cancel_price][1] == 0:
                    orderbook_vol.pop(cancel_price)
        
                if cancel_index in orderpool_lg:
                    orderbook_vol_lg[cancel_price][direction-1] -= trade_qty
                    if orderbook_vol_lg[cancel_price][0] == 0 and \
                       orderbook_vol_lg[cancel_price][1] == 0:
                        orderbook_vol_lg.pop(cancel_price)
        
                if bid1 and ask1 and whether_snapshot:
                    if len(sorted_price_levels) == 0 or len(sorted_price_levels) != len(orderbook) :
                        sorted_price_levels = list(sorted(orderbook.keys()))
        
                tick_type = 1
                tick_vol = trade_qty 
                tick_money = trade_qty * (cancel_price/1000)
                tick_direc = direction 

                target_tier = tier_dict[cancel_price]
                orderbook_tier[target_tier] -= trade_qty
        
        
            ################################
            ######### Transaction ##########
            ################################
                    
        
            elif data_type == 'transaction':
                trade_qty = int(raw.LastQty.iloc[idx])
                trade_price = int(round(raw.LastPx.iloc[idx]*1000))
                latest = trade_price
                trade_money = trade_price*0.001*trade_qty
                trade_vol = trade_qty
                cancel_vol = 0
                order_vol = 0
        
                order_aggr = 1
                
                buy_order = int(raw.BidApplSeqNum.iloc[idx])
                sell_order = int(raw.OfferApplSeqNum.iloc[idx])
                direction = 1 if buy_order > sell_order else 2
                
                buy_order_price, buy_order_qty, _ = orderpool[buy_order]
                sell_order_price, sell_order_qty, _ = orderpool[sell_order]
                
                if buy_order in orderpool_sm:
                    sm_order_cum_vol += trade_qty
                if sell_order in orderpool_sm:
                    sm_order_cum_vol -= trade_qty
        
                if buy_order in orderpool_mid:
                    mid_order_cum_vol += trade_qty
                if sell_order in orderpool_mid:
                    mid_order_cum_vol -= trade_qty
        
                if buy_order in orderpool_lg:
                    lg_order_cum_vol += trade_qty
                if sell_order in orderpool_lg:
                    lg_order_cum_vol -= trade_qty
        
                if buy_order in orderpool_slg:
                    slg_order_cum_vol += trade_qty
                if sell_order in orderpool_slg:
                    slg_order_cum_vol -= trade_qty
                        
                if trade_price in orderbook_vol:
                    original_trade_price_qty_buy = orderbook_vol[trade_price][0]
                    original_trade_price_qty_sell = orderbook_vol[trade_price][1]
                else:
                    original_trade_price_qty_buy = None
                    original_trade_price_qty_sell = None
            
                orderpool[buy_order][1] -= trade_qty
                orderpool[sell_order][1] -= trade_qty
                if orderpool[buy_order][1] < 0.1:
                    orderpool.pop(buy_order)
                if orderpool[sell_order][1] < 0.1:
                    orderpool.pop(sell_order)
        
                orderbook[buy_order_price][0][buy_order] -= trade_qty
                orderbook[sell_order_price][1][sell_order] -= trade_qty
                if orderbook[buy_order_price][0][buy_order] < 0.1:
                    orderbook[buy_order_price][0].pop(buy_order)
                if orderbook[sell_order_price][1][sell_order] < 0.1:
                    orderbook[sell_order_price][1].pop(sell_order)
        
                if len(orderbook[buy_order_price][0]) == len(orderbook[buy_order_price][1]) == 0:
                    orderbook.pop(buy_order_price)
                if sell_order_price in orderbook:
                    if len(orderbook[sell_order_price][0]) == len(orderbook[sell_order_price][1]) == 0:
                        orderbook.pop(sell_order_price)
        
                total_buy_qty -= trade_qty
                total_sell_qty -= trade_qty
        
                orderbook_vol[buy_order_price][0] -= trade_qty
                orderbook_vol[sell_order_price][1] -= trade_qty
        
                if orderbook_vol[buy_order_price][0] == 0 and \
                   orderbook_vol[buy_order_price][1] == 0:
                    orderbook_vol.pop(buy_order_price)
                if sell_order_price in orderbook_vol:
                    if orderbook_vol[sell_order_price][0] == 0 and \
                       orderbook_vol[sell_order_price][1] == 0:
                        orderbook_vol.pop(sell_order_price)
        
                if buy_order in orderpool_lg:
                    orderbook_vol_lg[buy_order_price][0] -= trade_qty
                    if orderbook_vol_lg[buy_order_price][0] == 0 and \
                       orderbook_vol_lg[buy_order_price][1] == 0:
                        orderbook_vol_lg.pop(buy_order_price)
        
                if sell_order in orderpool_lg:
                    orderbook_vol_lg[sell_order_price][1] -= trade_qty
                    if orderbook_vol_lg[sell_order_price][0] == 0 and \
                       orderbook_vol_lg[sell_order_price][1] == 0:
                        orderbook_vol_lg.pop(sell_order_price)
        
                if bid1 and ask1 and whether_snapshot:
                    if len(sorted_price_levels) == 0 or len(sorted_price_levels) != len(orderbook):
                        sorted_price_levels = list(sorted(orderbook.keys()))
        
                    if total_buy_qty == 0:
                        bid1 = sorted_price_levels[0]
                        ask1 = sorted_price_levels[0]
                    elif total_sell_qty == 0:
                        bid1 = sorted_price_levels[-1] 
                        ask1 = sorted_price_levels[-1]
                    else:     
                        if direction == 1:
                            for price in range(trade_price, upper_limit+increment, increment):
                                if price in orderbook_vol:
                                    if orderbook_vol[price][1] > 0:
                                        ask1 = price
                                        break
                            for price in range(ask1, lower_limit-increment, -increment):
                                if price in orderbook_vol:
                                    if orderbook_vol[price][0] > 0:
                                        bid1 = price
                                        break
            
                        elif direction == 2:
                            for price in range(trade_price, lower_limit-increment, -increment):
                                if price in orderbook_vol:
                                    if orderbook_vol[price][0] > 0:
                                        bid1 = price
                                        break
                            for price in range(bid1, upper_limit+increment, increment):
                                if price in orderbook_vol:
                                    if orderbook_vol[price][1] > 0:
                                        ask1 = price
                                        break
                    
                cum_money += trade_money     
                cum_vol += trade_qty
                
                tick_type = 2
                tick_vol = trade_qty 
                tick_money = trade_money
                tick_direc = direction 

                target_tier = tier_dict[buy_order_price]
                orderbook_tier[target_tier] -= trade_qty
                target_tier = tier_dict[sell_order_price]
                orderbook_tier[target_tier] -= trade_qty
                            
                  
            if idx == raw_len:
                sorted_price_levels = list(sorted(orderbook.keys()))
                if total_buy_qty == 0:
                    bid1 = sorted_price_levels[0]
                    bid1_index = 0
                    ask1 = sorted_price_levels[0]
                    ask1_index = 0
                elif total_sell_qty == 0:
                    bid1 = sorted_price_levels[-1]
                    bid1_index = len(sorted_price_levels)-1
                    ask1 = sorted_price_levels[-1]
                    ask1_index = len(sorted_price_levels)-1
                else:
                    num_lvs = len(sorted_price_levels)
                    for i in range(num_lvs):
                        bid = sorted_price_levels[max(i-1,0)]
                        ask = sorted_price_levels[max(i,0)]
                        if (len(orderbook[bid][1]) == 0 and \
                            len(orderbook[bid][0]) > 0 and \
                            len(orderbook[ask][0]) == 0 and \
                            len(orderbook[ask][1]) > 0): 
                            bid1 = bid
                            ask1 = ask
                            break  
        
        
            if whether_snapshot:
                
                bid1_price = bid1
                if bid1_price in orderbook_vol:
                    bid1_index = sorted_price_levels.index(bid1_price)
            
                ask1_price = ask1
                if ask1_price in orderbook_vol:
                    ask1_index = sorted_price_levels.index(ask1_price)

                if complete_orderbook:

                    a_time.append(time)
                    tick_info = []
                    tick_info += [
                        dict(sorted(copy.deepcopy(orderbook).items())[::-1]),
                    ]
                    df.append(tick_info)

                else:
        
                    bid1_price = bid1
                    if bid1_price in orderbook_vol:
                        bid1_index = sorted_price_levels.index(bid1_price)
                
                    ask1_price = ask1
                    if ask1_price in orderbook_vol:
                        ask1_index = sorted_price_levels.index(ask1_price)
            
                    mid_price = (bid1+ask1)/2
                    spread = ask1 - bid1
                    mid_prices.append(mid_price)
                    bid_prices = []
                    bid_qtys = []
                    ask_prices = []
                    ask_qtys = []
                    
                    for i in range(100):
                        try_bid_index = max(bid1_index-i, 0)
                        try_bid_price = sorted_price_levels[try_bid_index]
                        if orderbook_vol[try_bid_price][0] > 0 or (bid1_index-i) <= 0:
                            bid_prices.append(try_bid_price)
                            bid_qtys.append(orderbook_vol[try_bid_price][0])
                        if len(bid_prices) >= 10:
                            break
                    for i in range(100):
                        try_ask_index = min(ask1_index+i, len(sorted_price_levels)-1)
                        try_ask_price = sorted_price_levels[try_ask_index]
                        if orderbook_vol[try_ask_price][1] > 0 or (ask1_index+i) >= len(sorted_price_levels)-1:
                            ask_prices.append(try_ask_price)
                            ask_qtys.append(orderbook_vol[try_ask_price][1])
                        if len(ask_prices) >= 10:
                            break

                    bid_num_orders = [len(orderbook[p][0]) for p in bid_prices[:10]]
                    ask_num_orders = [len(orderbook[p][1]) for p in ask_prices[:10]]

                    bid_lg_qtys = [orderbook_vol_lg[p][0] if p in orderbook_vol_lg else 0 for p in bid_prices[:10]]
                    ask_lg_qtys = [orderbook_vol_lg[p][1] if p in orderbook_vol_lg else 0 for p in ask_prices[:10]]

                    price_tier_arr = [orderbook_tier[p] for p in orderbook_tier]

                    a_time.append(time)
                    tick_info = []
                    
                    tick_info += bid_prices
                    tick_info += bid_qtys
                    tick_info += ask_prices
                    tick_info += ask_qtys
                    tick_info += bid_num_orders
                    tick_info += ask_num_orders
                    tick_info += bid_lg_qtys
                    tick_info += ask_lg_qtys
                    tick_info += price_tier_arr
                    
                    mid_price = (bid1+ask1)/2
                    spread = ask1 - bid1
            
                    if tick_type == 2:
                        curr_money = cum_money - prev_cum_money
                        prev_cum_money = cum_money
                
                        curr_vol = cum_vol - prev_cum_vol
                        prev_cum_vol = cum_vol
            
                    if idx == raw_len:
                        tick_vol = curr_vol
                        tick_money = curr_money
                        tick_direc = 0
                        
                    tick_info += [prev_close, latest, mid_price, spread, \
                                  tick_type, tick_vol, tick_money, tick_direc, \
                                  order_aggr, \
                                  total_buy_qty, total_sell_qty, \
                                  sm_order_cum_vol, mid_order_cum_vol, lg_order_cum_vol, slg_order_cum_vol]
                    
                    df.append(tick_info)            
            
        orderbook = {key: orderbook[key] for key in sorted(orderbook.keys())[::-1]} 
        orderbook_vol = {key: orderbook_vol[key] for key in sorted(orderbook_vol.keys())[::-1]} 
        orderbook_vol_lg = {key: orderbook_vol_lg[key] for key in sorted(orderbook_vol_lg.keys())[::-1]} 
        
        a_time[-1 ] = date+' 15:00:00.000000'

        df = pd.DataFrame(np.array(df))
        df.columns = col_names   
        df.index = pd.to_datetime(a_time)
    
        # return df, orderbook_vol, orderbook_tier
        return df
    

def func(stock, date, show_progress_bar=True, complete_orderbook=False):

    try:
        df = reconstruct_orderbook(stock=stock, date=date, show_progress_bar=show_progress_bar, complete_orderbook=complete_orderbook)
        df.to_parquet('/A/MD/orderbook/processed/full/'+date+'/'+stock+'.parquet')
    except:
        flog = open('/A/log/ob/error_'+str(os.getpid())+'_'+date+'_'+stock+'.log','w')
        traceback.print_exc(file=flog)
    time.sleep(1)

today = datetime.datetime.now().date()
n_jobs = 40

print('Processing Data on ' + str(today))

if str(today) not in os.listdir('/A/MD/orderbook/processed/full'):
    os.mkdir('/A/MD/orderbook/processed/full/'+str(today))

stocks = [s.split('.')[0] for s in os.listdir('/A/MD/orderbook/raw/'+str(today))]

res = Parallel(n_jobs=n_jobs)(delayed(func)(stock=stock, date=str(today), show_progress_bar=False, complete_orderbook=False) for stock in tqdm(stocks))   

print('Orderbook Reconstruction Complete!')