from nb_common import *
from tqdm.notebook import tqdm
import os
from joblib import Parallel, delayed


today = str(datetime.datetime.today().date())

all_dts = np.sort(os.listdir('/A/MD/tao'))

etf_sh = np.array([510050,510300,510310,510330,510500,588000,588080,512100])
etf_sz = np.array([159915,159919,159922])

############## SH ##############


def func(dt):

    if dt < '2025-06-12':
        data = pd.read_csv('/A/MD/tao2/'+dt+'/mdl_4_24_0.csv')
    else:
        if dt < '2025-06-23':
            data = pd.read_csv('/A/MD/tao/'+dt+'/dy_sh_transorder.csv')
        else:
            data = pd.read_csv('/A/MD/tao/'+dt+'/sh_transorder.csv')

    stock_list = data.SecurityID.unique()
    stock_list = np.concatenate((stock_list[(stock_list < 700000) & (stock_list > 600000)], etf_sh))

    for stock in tqdm(stock_list[:]):

        stock_data = data[data.SecurityID == stock].sort_values(by = ['BizIndex'])
        if 'SUSP' in stock_data.TickBSFlag.value_counts():
            continue

        if dt not in os.listdir('/A/MD/orderbook/raw'):
            os.makedirs('/A/MD/orderbook/raw/' + dt)

        stock_data.to_parquet('/A/MD/orderbook/raw/' + dt + '/' + str(stock) + '.parquet')

func(today)


############## SZ ##############

def func(dt):

    if dt < '2025-06-12':
        data1 = pd.read_csv('/A/MD/tao2/'+dt+'/mdl_6_33_0.csv')
        data2 = pd.read_csv('/A/MD/tao2/'+dt+'/mdl_6_36_0.csv')

        channel_no = data1.index
        data1.columns = list(data1.columns[1:])+['misc']
        data1['ChannelNo'] = channel_no
        data1.index = np.arange(data1.shape[0])
        data1 = data1[[data1.columns[-1]] + list(data1.columns)[:-1]]

        channel_no = data2.index
        data2.columns = list(data2.columns[1:])+['misc']
        data2['ChannelNo'] = channel_no
        data2.index = np.arange(data1.shape[0], data1.shape[0]+data2.shape[0])
        data2 = data2[[data2.columns[-1]] + list(data2.columns)[:-1]]

        data = pd.concat((data1, data2))

    else:

        if dt < '2025-06-23':
            data1 = pd.read_csv('/A/MD/tao/'+dt+'/dy_sz_trans.csv')
            data2 = pd.read_csv('/A/MD/tao/'+dt+'/dy_sz_order.csv')
            data = pd.concat((data1, data2))
        else:
            data1 = pd.read_csv('/A/MD/tao/'+dt+'/sz_trans.csv')
            data2 = pd.read_csv('/A/MD/tao/'+dt+'/sz_order.csv')
            data = pd.concat((data1, data2))           

    del data1
    del data2

    stock_list = data.SecurityID.unique()
    stock_list = stock_list[((stock_list>300000)&(stock_list < 302000)) | (stock_list<4000)]
    stock_list = list(stock_list) + list(etf_sz)

    for stock in tqdm(stock_list[:]):

        stock_ = (6-len(str(stock)))*'0'+str(stock)
        
        stock_data = data[data.SecurityID == stock].sort_values(by = ['ApplSeqNum'])

        if stock_data.shape[0] < 1000:
            continue

        if dt not in os.listdir('/A/MD/orderbook/raw'):
            os.makedirs('/A/MD/orderbook/raw/' + dt)

        stock_data.to_parquet('/A/MD/orderbook/raw/' + dt + '/' + str(stock_) + '.parquet')
            
    del data

func(today)

