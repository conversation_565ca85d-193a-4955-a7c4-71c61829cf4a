from nb_common import *
from scipy.optimize import newton
from scipy.stats import norm
import os
import calendar
import math
from joblib import <PERSON>llel, delayed
from tqdm import tqdm
from scipy.optimize import minimize
from ut_redis import FrRedis


def get_all_op_ex_dates():
    full_info = pd.read_pickle(f'{MDLSTDIR}/all_securities/full.pickle')
    all_options_ex_dates = sorted(
        full_info[(full_info.type == 'options') & (full_info.name.str[:2] == '10')].end_date.dt.strftime(
            "%Y-%m-%d").unique())
    all_options_ex_dates = np.array(
        [datetime.datetime.strptime(date, "%Y-%m-%d").date() for date in all_options_ex_dates])

    return all_options_ex_dates


def get_month(date):

    full_info = pd.read_pickle(f'{MDLSTDIR}/all_securities/full.pickle')
    all_options_ex_dates = sorted(full_info[(full_info.type == 'options') & (full_info.name.str[:2] == '10')].end_date.dt.strftime("%Y-%m-%d").unique())
    all_options_ex_dates = np.array([datetime.datetime.strptime(date, "%Y-%m-%d").date() for date in all_options_ex_dates]) 
    next_ex_dt = str(np.sort(all_options_ex_dates[all_options_ex_dates >= date])[0]).split('-')
    next_ex_dt = next_ex_dt[0][-2:] + next_ex_dt[1]

    return next_ex_dt



def preopen_op_info(underlying, date):
    mdi = pickle_read_mdi(str(date))
    
    curr_month = mdi.opkind[underlying]['a_yymm'][0]
    next_month = mdi.opkind[underlying]['a_yymm'][1]
        
    con_idx = []
    strike = []
    con_type = []
    exp_dt = []
    for cp in ['C', 'P']:
        for month_idx, month in enumerate([curr_month, next_month]):
            all_k =  mdi.opkind[underlying]['d_yymm_pxu_M'][month]
            for k in all_k:
                con_idx.append(mdi.oplstt.xs((underlying, month, "M", k))[cp])
                strike.append(int(k[1:]))
                con_type.append(cp)
                exp_dt.append(month_idx)
    op_info = pd.DataFrame()
    op_info['id'] = con_idx
    op_info['strike'] = strike
    op_info['option_type'] = con_type
    op_info['exp_dt'] = exp_dt
    op_info = op_info.set_index('id')

    daily_underlying_price = pickle_read_md1m_s(underlying).loc[date:datetime.datetime.combine(date, datetime.time(15)), 'avg'] * 1000

    daily_timestamps = pickle_read_md1m_s(underlying).loc[date:datetime.datetime.combine(date, datetime.time(15)), 'avg'].index
    daily_timestamps = [datetime.datetime.strptime(str(t), "%Y-%m-%d %H:%M:%S") for t in daily_timestamps]

    if underlying in ['159915','510500'] and date <= datetime.date(2022,10,1):
        date = datetime.date(2022,10,1)
    full_info = pd.read_pickle(f'{MDLSTDIR}/all_securities/full.pickle')
    all_options_ex_dates = sorted(full_info[(full_info.type == 'options') & (full_info.name.str[:2] == '10')].end_date.dt.strftime("%Y-%m-%d").unique())
    all_options_ex_dates = np.array([datetime.datetime.strptime(date, "%Y-%m-%d").date() for date in all_options_ex_dates])
    exdt1, exdt2 = all_options_ex_dates[all_options_ex_dates>=date][:2]
    exdt1_yr, exdt1_mon, exdt1_d = exdt1.year, exdt1.month, exdt1.day
    exdt2_yr, exdt2_mon, exdt2_d = exdt2.year, exdt2.month, exdt2.day
    exdt1 = datetime.datetime(exdt1_yr, exdt1_mon, exdt1_d, 16,0,0)
    exdt2 = datetime.datetime(exdt2_yr, exdt2_mon, exdt2_d, 16,0,0)

    daily_op_info = op_info
    op_pair = {}
    for con in daily_op_info.index:
        the_strike = daily_op_info.loc[con, 'strike']
        the_mon = daily_op_info.loc[con, 'exp_dt']
        c, p = daily_op_info[(daily_op_info.strike==the_strike)&(daily_op_info.exp_dt==the_mon)].sort_values(by=['option_type']).index
        if c in op_pair:
            op_pair[p] = c
        else:
            op_pair[c] = p

    op_info.strike = op_info.strike / 1e3
    daily_underlying_price = daily_underlying_price/1e3
    op_info = op_info.reset_index()
        
    return op_info, daily_underlying_price, daily_timestamps, op_pair, (exdt1, exdt2)



def replenish_op_contracts(df):
    additional = []
    for idx in range(df.shape[0]-1):
        k1, k2 = df.iloc[idx,1], df.iloc[idx+1,1]
        t = df.iloc[idx,3]
        id = df.iloc[idx,0]
        con_type1, con_type2 = df.iloc[idx,2], df.iloc[idx+1,2]
        p1, p2 = df.iloc[idx,4], df.iloc[idx+1,4]
        if con_type1 == con_type2:
            for k in np.linspace(k1,k2,round((k2-k1)*1e2)+1):
                new_price = (p2*(k-k1)+p1*(k2-k)) / (k2-k1)
                new_line = ['interpolated', k, con_type1, t, new_price]
                if k not in [k1, k2]:
                    additional.append(new_line)
    additional = pd.DataFrame(np.array(additional), columns = df.columns)
    added_df = pd.concat([additional, df])
    added_df.strike = added_df.strike.astype(float)
    added_df.exp_dt = added_df.exp_dt.astype(int)
    added_df.price = added_df.price.astype(float)
    added_df = added_df.sort_values(by=['option_type','strike'], ascending=[False,True])
    
    return added_df.reset_index(drop=True)
    



def get_live_vix(static_info, date, t, R=0.02):

    daily_op_info, daily_underlying_price, daily_timestamps, op_pair, exdt = static_info

    T1 = (exdt[0]-daily_timestamps[t]).total_seconds() / (365*24*60*60)
    T2 = (exdt[1]-daily_timestamps[t]).total_seconds() / (365*24*60*60)

    curr_op_info = (daily_op_info[daily_op_info.exp_dt == 0]).sort_values(by=['option_type','strike'], ascending=[False, True])
    next_op_info = (daily_op_info[daily_op_info.exp_dt == 1]).sort_values(by=['option_type','strike'], ascending=[False, True])

    curr_op_info['price'] = [pickle_read_md1m_o(con).loc[str(date)].avg.values[t]/1e4 for con in curr_op_info.id]
    next_op_info['price'] = [pickle_read_md1m_o(con).loc[str(date)].avg.values[t]/1e4 for con in next_op_info.id]

    curr_op_info = replenish_op_contracts(curr_op_info) 
    next_op_info = replenish_op_contracts(next_op_info) 

    daily_op_info = pd.concat([curr_op_info, next_op_info]).reset_index(drop=True)

    daily_op_info['distance'] = daily_op_info.strike - daily_underlying_price[t]
    curr_otm_op_info = pd.concat((daily_op_info[(daily_op_info.exp_dt==0)&(daily_op_info.option_type=='C')&(daily_op_info.distance>=-1e-6)],\
                       daily_op_info[(daily_op_info.exp_dt==0)&(daily_op_info.option_type=='P')&(daily_op_info.distance<=1e-6)]))\
                       .sort_values(by=['strike','option_type'], ascending=[True, False])
    next_otm_op_info = pd.concat((daily_op_info[(daily_op_info.exp_dt==1)&(daily_op_info.option_type=='C')&(daily_op_info.distance>=-1e-6)],\
                       daily_op_info[(daily_op_info.exp_dt==1)&(daily_op_info.option_type=='P')&(daily_op_info.distance<=1e-6)]))\
                       .sort_values(by=['strike','option_type'], ascending=[True, False])
    
    curr_otm_op_info['counter_contract_price'] = [pickle_read_md1m_o(op_pair[con]).loc[str(date)].avg.values[t]/1e4 if con != 'interpolated' else None \
                                                  for con in curr_otm_op_info.id]
    next_otm_op_info['counter_contract_price'] = [pickle_read_md1m_o(op_pair[con]).loc[str(date)].avg.values[t]/1e4 if con != 'interpolated' else None \
                                                  for con in next_otm_op_info.id]

    curr_otm_op_info['F'] = [curr_otm_op_info['price'].iloc[idx]-curr_otm_op_info['counter_contract_price'].iloc[idx]+curr_otm_op_info['strike'].iloc[idx] \
                            if curr_otm_op_info['option_type'].iloc[idx]=='C' \
                            else curr_otm_op_info['counter_contract_price'].iloc[idx]-curr_otm_op_info['price'].iloc[idx]+curr_otm_op_info['strike'].iloc[idx] \
                                                                                                                         for idx in range(curr_otm_op_info.shape[0])]
            
    next_otm_op_info['F'] = [next_otm_op_info['price'].iloc[idx]-next_otm_op_info['counter_contract_price'].iloc[idx]+next_otm_op_info['strike'].iloc[idx] \
                            if next_otm_op_info['option_type'].iloc[idx]=='C' \
                            else next_otm_op_info['counter_contract_price'].iloc[idx]-next_otm_op_info['price'].iloc[idx]+next_otm_op_info['strike'].iloc[idx] \
                                                                                                                         for idx in range(next_otm_op_info.shape[0])]
    F1 = curr_otm_op_info['F'].dropna().mean()
    F2 = next_otm_op_info['F'].dropna().mean()
    
    strike_arr = curr_otm_op_info.strike.values
    delta_ki = np.concatenate((np.array([strike_arr[1]-strike_arr[0]]),(strike_arr[2:] - strike_arr[:-2])*0.5, np.array([strike_arr[-1]-strike_arr[-2]])))
    curr_otm_op_info['delta_ki'] = delta_ki
    
    strike_arr = next_otm_op_info.strike.values
    delta_ki = np.concatenate((np.array([strike_arr[1]-strike_arr[0]]),(strike_arr[2:] - strike_arr[:-2])*0.5, np.array([strike_arr[-1]-strike_arr[-2]])))
    next_otm_op_info['delta_ki'] = delta_ki

    first_term_1 = (2/T1)*np.sum((curr_otm_op_info.delta_ki.values / curr_otm_op_info.strike.values**2)*np.exp(R*T1)*curr_otm_op_info.price.values)
    
    second_term_1 = (1/T1)*(F1/daily_underlying_price[t]-1)**2
        
    sigma_sqr_1 = first_term_1 - second_term_1
    
    first_term_2 = (2/T2)*np.sum((next_otm_op_info.delta_ki.values / next_otm_op_info.strike.values**2)*np.exp(R*T2)*next_otm_op_info.price.values)
    
    second_term_2 = (1/T2)*(F2/daily_underlying_price[t]-1)**2
    
    sigma_sqr_2 = first_term_2 - second_term_2
    
    N_T1 = int(T1*(365*24*60))
    N_T2 = int(T2*(365*24*60))
    N_30 = 43200
    N_365 = 525600
    
    frac1 = (N_T2-N_30) / (N_T2-N_T1)
    frac2 = (N_30-N_T1) / (N_T2-N_T1)
    
    vix = 100*((T1*sigma_sqr_1*frac1 + T2*sigma_sqr_2*frac2) * (N_365/N_30))**0.5

    return vix
    


date_dict = {
    '510050': datetime.date(2020,1,2), 
    '510300': datetime.date(2020,1,2), 
    '159919': datetime.date(2020,1,2), 
    '510500': datetime.date(2022,9,19), 
    '159922': datetime.date(2022,9,19), 
    '159915': datetime.date(2022,9,19), 
    '588000': datetime.date(2023,6,5), 
    '588080': datetime.date(2023,6,5), 
}


underlying_dict = {
    '510050': '50ETF', 
    '159919': '919ETF', 
    '510300': '300ETF', 
    '159922': '922ETF', 
    '510500': '500ETF', 
    '159915': '915ETF', 
    '588000': 'k50ETF', 
    '588080': 'k58ETF', 
}

def prep_data(underlying, st_dt, ed_dt, n_jobs=1):

    all_t_days = pickle_read_tdays()[:-1]
    days = all_t_days[(all_t_days >= st_dt) & (all_t_days <= ed_dt)]
    
    vix = {'abs':{}, 'b':{}}
    for date in tqdm(days):

        curr_mon = get_month(date)

        if curr_mon not in vix['abs']:
            vix['abs'][curr_mon] = []
            vix['b'][curr_mon] = []
        
        static_info = preopen_op_info(underlying, date)
        
        daily_vix_lst = Parallel(n_jobs=n_jobs)(delayed(get_live_vix)(static_info, date, t, R=0.02) for t in range(240))

        daily_vix = daily_vix_lst
        daily_b = pickle_read_md1m_s(underlying).loc[str(date)].avg
        vix_df = pd.Series(daily_vix, index=daily_b.index)
        vix['abs'][curr_mon].append(vix_df)
        vix['b'][curr_mon].append(daily_b)
       

    for mon in vix['abs'].keys():
        vix['abs'][mon] = pd.concat(vix['abs'][mon])
        vix['b'][mon] = pd.concat(vix['b'][mon])
    
    return vix


all_op_exp_dts = get_all_op_ex_dates()
all_t_days = pickle_read_tdays()
last_op_exp_dt = np.sort(all_op_exp_dts[all_op_exp_dts < datetime.date.today()])[-1]
last_op_exp_dt_idx = np.argwhere(all_t_days == last_op_exp_dt)[0][0]
month_first_dt = all_t_days[last_op_exp_dt_idx+1]


vix_50 = prep_data(underlying='510050', st_dt=month_first_dt, ed_dt=datetime.date.today(), n_jobs=10)
vix_300 = prep_data(underlying='510300', st_dt=month_first_dt, ed_dt=datetime.date.today(), n_jobs=10)
vix_500 = prep_data(underlying='510500', st_dt=month_first_dt, ed_dt=datetime.date.today(), n_jobs=10)
vix_915 = prep_data(underlying='159915', st_dt=month_first_dt, ed_dt=datetime.date.today(), n_jobs=10)
vix_k50 = prep_data(underlying='588000', st_dt=month_first_dt, ed_dt=datetime.date.today(), n_jobs=10)


######################################################

with open('/A/MD/A_history/vix_50.pkl', 'rb') as file:
    new_vix_50 = pickle.load(file)

for mon in vix_50['abs'].keys():
    new_vix_50['abs'][mon] = vix_50['abs'][mon]
    new_vix_50['b'][mon] = vix_50['b'][mon]

with open('/A/MD/A_history/vix_50.pkl', 'wb') as file:
    pickle.dump(new_vix_50, file)

######################################################

with open('/A/MD/A_history/vix_300.pkl', 'rb') as file:
    new_vix_300 = pickle.load(file)

for mon in vix_300['abs'].keys():
    new_vix_300['abs'][mon] = vix_300['abs'][mon]
    new_vix_300['b'][mon] = vix_300['b'][mon]

with open('/A/MD/A_history/vix_300.pkl', 'wb') as file:
    pickle.dump(new_vix_300, file)

######################################################

with open('/A/MD/A_history/vix_500.pkl', 'rb') as file:
    new_vix_500 = pickle.load(file)

for mon in vix_500['abs'].keys():
    new_vix_500['abs'][mon] = vix_500['abs'][mon]
    new_vix_500['b'][mon] = vix_500['b'][mon]

with open('/A/MD/A_history/vix_500.pkl', 'wb') as file:
    pickle.dump(new_vix_500, file)

######################################################

with open('/A/MD/A_history/vix_915.pkl', 'rb') as file:
    new_vix_915 = pickle.load(file)

for mon in vix_915['abs'].keys():
    new_vix_915['abs'][mon] = vix_915['abs'][mon]
    new_vix_915['b'][mon] = vix_915['b'][mon]

with open('/A/MD/A_history/vix_915.pkl', 'wb') as file:
    pickle.dump(new_vix_915, file)

######################################################

with open('/A/MD/A_history/vix_k50.pkl', 'rb') as file:
    new_vix_k50 = pickle.load(file)

for mon in vix_k50['abs'].keys():
    new_vix_k50['abs'][mon] = vix_k50['abs'][mon]
    new_vix_k50['b'][mon] = vix_k50['b'][mon]

with open('/A/MD/A_history/vix_k50.pkl', 'wb') as file:
    pickle.dump(new_vix_k50, file)

print('COMPLETE!')